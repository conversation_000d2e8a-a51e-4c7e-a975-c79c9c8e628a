Analyzing 2 items...                                            

   info • 'InternetConnectionRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/core/providers/simple_internet_provider.dart:9:33 • deprecated_member_use_from_same_package
   info • 'InternetStatusRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/core/providers/simple_internet_provider.dart:19:29 • deprecated_member_use_from_same_package
   info • 'DeadLetterQueueServiceRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/core/services/dead_letter_queue_service.dart:13:47 • deprecated_member_use_from_same_package
warning • This default clause is covered by the previous cases • lib/core/services/error_handling_service.dart:105:7 • unreachable_switch_default
   info • 'GlobalErrorHandlerRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/core/services/global_error_handler.dart:26:39 • deprecated_member_use_from_same_package
   info • 'GracefulDegradationServiceRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/core/services/graceful_degradation_service.dart:28:3 • deprecated_member_use_from_same_package
   info • 'RetryServiceRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/core/services/retry_service.dart:22:27 • deprecated_member_use_from_same_package
warning • The value of the field '_retryDelay' isn't used • lib/core/services/smart_backend_service.dart:89:25 • unused_field
   info • 'TimeoutServiceRef' is deprecated and shouldn't be used. Will be removed in 3.0. Use Ref instead • lib/core/services/timeout_service.dart:21:31 • deprecated_member_use_from_same_package

9 issues found. (ran in 1.7s)
