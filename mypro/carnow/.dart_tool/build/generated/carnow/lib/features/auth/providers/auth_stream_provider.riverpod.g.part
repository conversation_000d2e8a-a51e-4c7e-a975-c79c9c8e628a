// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authStreamHash() => r'c8ea94961ebda2d6d6bcfe948da3474f4b099505';

/// Stream provider for authentication state changes
///
/// Copied from [authStream].
@ProviderFor(authStream)
final authStreamProvider = AutoDisposeStreamProvider<SimpleAuthState>.internal(
  authStream,
  name: r'authStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthStreamRef = AutoDisposeStreamProviderRef<SimpleAuthState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
