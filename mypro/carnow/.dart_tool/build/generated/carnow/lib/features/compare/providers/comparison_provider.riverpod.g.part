// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activeComparisonHash() => r'22aeedd63d165834142e7ecac354efe467c64f94';

/// See also [activeComparison].
@ProviderFor(activeComparison)
final activeComparisonProvider =
    AutoDisposeFutureProvider<ProductComparison?>.internal(
      activeComparison,
      name: r'activeComparisonProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$activeComparisonHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActiveComparisonRef = AutoDisposeFutureProviderRef<ProductComparison?>;
String _$savedComparisonsHash() => r'f8d5a2916ff79ca7781b19b23659f004201da7ce';

/// See also [savedComparisons].
@ProviderFor(savedComparisons)
final savedComparisonsProvider =
    AutoDisposeFutureProvider<List<ProductComparison>>.internal(
      savedComparisons,
      name: r'savedComparisonsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$savedComparisonsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SavedComparisonsRef =
    AutoDisposeFutureProviderRef<List<ProductComparison>>;
String _$comparisonManagerHash() => r'509976ca1d41f7fe0cc84686d83946a507c6541a';

/// See also [ComparisonManager].
@ProviderFor(ComparisonManager)
final comparisonManagerProvider =
    AutoDisposeAsyncNotifierProvider<
      ComparisonManager,
      List<ProductComparison>
    >.internal(
      ComparisonManager.new,
      name: r'comparisonManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$comparisonManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ComparisonManager = AutoDisposeAsyncNotifier<List<ProductComparison>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
