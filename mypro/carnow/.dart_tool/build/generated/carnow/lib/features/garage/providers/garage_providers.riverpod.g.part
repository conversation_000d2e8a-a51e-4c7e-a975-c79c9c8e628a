// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$garageRepositoryHash() => r'a4beb7979393435e092d2ac38b50366f5367c395';

/// See also [garageRepository].
@ProviderFor(garageRepository)
final garageRepositoryProvider = AutoDisposeProvider<GarageRepository>.internal(
  garageRepository,
  name: r'garageRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$garageRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GarageRepositoryRef = AutoDisposeProviderRef<GarageRepository>;
String _$garageServiceHash() => r'bf9bd9d3ff3c63991687bf7201557fe41246ef74';

/// See also [garageService].
@ProviderFor(garageService)
final garageServiceProvider = AutoDisposeProvider<GarageService>.internal(
  garageService,
  name: r'garageServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$garageServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GarageServiceRef = AutoDisposeProviderRef<GarageService>;
String _$vehicleMakesHash() => r'b3363f9653599af2e06e2560f89cda10779227bb';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [vehicleMakes].
@ProviderFor(vehicleMakes)
const vehicleMakesProvider = VehicleMakesFamily();

/// See also [vehicleMakes].
class VehicleMakesFamily extends Family<AsyncValue<List<VehicleMake>>> {
  /// See also [vehicleMakes].
  const VehicleMakesFamily();

  /// See also [vehicleMakes].
  VehicleMakesProvider call({String? search}) {
    return VehicleMakesProvider(search: search);
  }

  @override
  VehicleMakesProvider getProviderOverride(
    covariant VehicleMakesProvider provider,
  ) {
    return call(search: provider.search);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleMakesProvider';
}

/// See also [vehicleMakes].
class VehicleMakesProvider
    extends AutoDisposeFutureProvider<List<VehicleMake>> {
  /// See also [vehicleMakes].
  VehicleMakesProvider({String? search})
    : this._internal(
        (ref) => vehicleMakes(ref as VehicleMakesRef, search: search),
        from: vehicleMakesProvider,
        name: r'vehicleMakesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleMakesHash,
        dependencies: VehicleMakesFamily._dependencies,
        allTransitiveDependencies:
            VehicleMakesFamily._allTransitiveDependencies,
        search: search,
      );

  VehicleMakesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.search,
  }) : super.internal();

  final String? search;

  @override
  Override overrideWith(
    FutureOr<List<VehicleMake>> Function(VehicleMakesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleMakesProvider._internal(
        (ref) => create(ref as VehicleMakesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        search: search,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleMake>> createElement() {
    return _VehicleMakesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleMakesProvider && other.search == search;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, search.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleMakesRef on AutoDisposeFutureProviderRef<List<VehicleMake>> {
  /// The parameter `search` of this provider.
  String? get search;
}

class _VehicleMakesProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleMake>>
    with VehicleMakesRef {
  _VehicleMakesProviderElement(super.provider);

  @override
  String? get search => (origin as VehicleMakesProvider).search;
}

String _$vehicleModelsHash() => r'9f42ac6d8b7d832f4f428f6b7917ba9f722a6690';

/// See also [vehicleModels].
@ProviderFor(vehicleModels)
const vehicleModelsProvider = VehicleModelsFamily();

/// See also [vehicleModels].
class VehicleModelsFamily extends Family<AsyncValue<List<VehicleModel>>> {
  /// See also [vehicleModels].
  const VehicleModelsFamily();

  /// See also [vehicleModels].
  VehicleModelsProvider call(int makeId, {String? search}) {
    return VehicleModelsProvider(makeId, search: search);
  }

  @override
  VehicleModelsProvider getProviderOverride(
    covariant VehicleModelsProvider provider,
  ) {
    return call(provider.makeId, search: provider.search);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleModelsProvider';
}

/// See also [vehicleModels].
class VehicleModelsProvider
    extends AutoDisposeFutureProvider<List<VehicleModel>> {
  /// See also [vehicleModels].
  VehicleModelsProvider(int makeId, {String? search})
    : this._internal(
        (ref) => vehicleModels(ref as VehicleModelsRef, makeId, search: search),
        from: vehicleModelsProvider,
        name: r'vehicleModelsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleModelsHash,
        dependencies: VehicleModelsFamily._dependencies,
        allTransitiveDependencies:
            VehicleModelsFamily._allTransitiveDependencies,
        makeId: makeId,
        search: search,
      );

  VehicleModelsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.makeId,
    required this.search,
  }) : super.internal();

  final int makeId;
  final String? search;

  @override
  Override overrideWith(
    FutureOr<List<VehicleModel>> Function(VehicleModelsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleModelsProvider._internal(
        (ref) => create(ref as VehicleModelsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        makeId: makeId,
        search: search,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleModel>> createElement() {
    return _VehicleModelsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleModelsProvider &&
        other.makeId == makeId &&
        other.search == search;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, makeId.hashCode);
    hash = _SystemHash.combine(hash, search.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleModelsRef on AutoDisposeFutureProviderRef<List<VehicleModel>> {
  /// The parameter `makeId` of this provider.
  int get makeId;

  /// The parameter `search` of this provider.
  String? get search;
}

class _VehicleModelsProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleModel>>
    with VehicleModelsRef {
  _VehicleModelsProviderElement(super.provider);

  @override
  int get makeId => (origin as VehicleModelsProvider).makeId;
  @override
  String? get search => (origin as VehicleModelsProvider).search;
}

String _$vehicleYearsHash() => r'c1c1fca7b55dcb35cad9ed6836e1f61c8022a58e';

/// See also [vehicleYears].
@ProviderFor(vehicleYears)
const vehicleYearsProvider = VehicleYearsFamily();

/// See also [vehicleYears].
class VehicleYearsFamily extends Family<AsyncValue<List<VehicleYear>>> {
  /// See also [vehicleYears].
  const VehicleYearsFamily();

  /// See also [vehicleYears].
  VehicleYearsProvider call(int modelId) {
    return VehicleYearsProvider(modelId);
  }

  @override
  VehicleYearsProvider getProviderOverride(
    covariant VehicleYearsProvider provider,
  ) {
    return call(provider.modelId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleYearsProvider';
}

/// See also [vehicleYears].
class VehicleYearsProvider
    extends AutoDisposeFutureProvider<List<VehicleYear>> {
  /// See also [vehicleYears].
  VehicleYearsProvider(int modelId)
    : this._internal(
        (ref) => vehicleYears(ref as VehicleYearsRef, modelId),
        from: vehicleYearsProvider,
        name: r'vehicleYearsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleYearsHash,
        dependencies: VehicleYearsFamily._dependencies,
        allTransitiveDependencies:
            VehicleYearsFamily._allTransitiveDependencies,
        modelId: modelId,
      );

  VehicleYearsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.modelId,
  }) : super.internal();

  final int modelId;

  @override
  Override overrideWith(
    FutureOr<List<VehicleYear>> Function(VehicleYearsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleYearsProvider._internal(
        (ref) => create(ref as VehicleYearsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        modelId: modelId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleYear>> createElement() {
    return _VehicleYearsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleYearsProvider && other.modelId == modelId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, modelId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleYearsRef on AutoDisposeFutureProviderRef<List<VehicleYear>> {
  /// The parameter `modelId` of this provider.
  int get modelId;
}

class _VehicleYearsProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleYear>>
    with VehicleYearsRef {
  _VehicleYearsProviderElement(super.provider);

  @override
  int get modelId => (origin as VehicleYearsProvider).modelId;
}

String _$vehicleTrimsHash() => r'63cc9eb8c671cec9e37d6f96c29a62e16860aec5';

/// See also [vehicleTrims].
@ProviderFor(vehicleTrims)
const vehicleTrimsProvider = VehicleTrimsFamily();

/// See also [vehicleTrims].
class VehicleTrimsFamily extends Family<AsyncValue<List<VehicleTrim>>> {
  /// See also [vehicleTrims].
  const VehicleTrimsFamily();

  /// See also [vehicleTrims].
  VehicleTrimsProvider call(int modelId) {
    return VehicleTrimsProvider(modelId);
  }

  @override
  VehicleTrimsProvider getProviderOverride(
    covariant VehicleTrimsProvider provider,
  ) {
    return call(provider.modelId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleTrimsProvider';
}

/// See also [vehicleTrims].
class VehicleTrimsProvider
    extends AutoDisposeFutureProvider<List<VehicleTrim>> {
  /// See also [vehicleTrims].
  VehicleTrimsProvider(int modelId)
    : this._internal(
        (ref) => vehicleTrims(ref as VehicleTrimsRef, modelId),
        from: vehicleTrimsProvider,
        name: r'vehicleTrimsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleTrimsHash,
        dependencies: VehicleTrimsFamily._dependencies,
        allTransitiveDependencies:
            VehicleTrimsFamily._allTransitiveDependencies,
        modelId: modelId,
      );

  VehicleTrimsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.modelId,
  }) : super.internal();

  final int modelId;

  @override
  Override overrideWith(
    FutureOr<List<VehicleTrim>> Function(VehicleTrimsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleTrimsProvider._internal(
        (ref) => create(ref as VehicleTrimsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        modelId: modelId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleTrim>> createElement() {
    return _VehicleTrimsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleTrimsProvider && other.modelId == modelId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, modelId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleTrimsRef on AutoDisposeFutureProviderRef<List<VehicleTrim>> {
  /// The parameter `modelId` of this provider.
  int get modelId;
}

class _VehicleTrimsProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleTrim>>
    with VehicleTrimsRef {
  _VehicleTrimsProviderElement(super.provider);

  @override
  int get modelId => (origin as VehicleTrimsProvider).modelId;
}

String _$vehicleEnginesHash() => r'fe14070694ea01e045b5b07fb90bd628f72302ea';

/// See also [vehicleEngines].
@ProviderFor(vehicleEngines)
const vehicleEnginesProvider = VehicleEnginesFamily();

/// See also [vehicleEngines].
class VehicleEnginesFamily extends Family<AsyncValue<List<VehicleEngine>>> {
  /// See also [vehicleEngines].
  const VehicleEnginesFamily();

  /// See also [vehicleEngines].
  VehicleEnginesProvider call(int modelId) {
    return VehicleEnginesProvider(modelId);
  }

  @override
  VehicleEnginesProvider getProviderOverride(
    covariant VehicleEnginesProvider provider,
  ) {
    return call(provider.modelId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'vehicleEnginesProvider';
}

/// See also [vehicleEngines].
class VehicleEnginesProvider
    extends AutoDisposeFutureProvider<List<VehicleEngine>> {
  /// See also [vehicleEngines].
  VehicleEnginesProvider(int modelId)
    : this._internal(
        (ref) => vehicleEngines(ref as VehicleEnginesRef, modelId),
        from: vehicleEnginesProvider,
        name: r'vehicleEnginesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$vehicleEnginesHash,
        dependencies: VehicleEnginesFamily._dependencies,
        allTransitiveDependencies:
            VehicleEnginesFamily._allTransitiveDependencies,
        modelId: modelId,
      );

  VehicleEnginesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.modelId,
  }) : super.internal();

  final int modelId;

  @override
  Override overrideWith(
    FutureOr<List<VehicleEngine>> Function(VehicleEnginesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VehicleEnginesProvider._internal(
        (ref) => create(ref as VehicleEnginesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        modelId: modelId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleEngine>> createElement() {
    return _VehicleEnginesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VehicleEnginesProvider && other.modelId == modelId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, modelId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin VehicleEnginesRef on AutoDisposeFutureProviderRef<List<VehicleEngine>> {
  /// The parameter `modelId` of this provider.
  int get modelId;
}

class _VehicleEnginesProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleEngine>>
    with VehicleEnginesRef {
  _VehicleEnginesProviderElement(super.provider);

  @override
  int get modelId => (origin as VehicleEnginesProvider).modelId;
}

String _$myVehiclesHash() => r'8e50eb7630708ea4a75098238b6e68ae160f30a6';

/// See also [myVehicles].
@ProviderFor(myVehicles)
final myVehiclesProvider =
    AutoDisposeFutureProvider<List<UserVehicle>>.internal(
      myVehicles,
      name: r'myVehiclesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$myVehiclesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MyVehiclesRef = AutoDisposeFutureProviderRef<List<UserVehicle>>;
String _$primaryVehicleHash() => r'ac166481ccebb430dc96d3615a14007f5f18868d';

/// See also [primaryVehicle].
@ProviderFor(primaryVehicle)
final primaryVehicleProvider = AutoDisposeFutureProvider<UserVehicle?>.internal(
  primaryVehicle,
  name: r'primaryVehicleProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$primaryVehicleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PrimaryVehicleRef = AutoDisposeFutureProviderRef<UserVehicle?>;
String _$vehicleSelectionStateHash() =>
    r'cb2e4c725748565aba948f60cfab5e2d9ff2b5c3';

/// See also [VehicleSelectionState].
@ProviderFor(VehicleSelectionState)
final vehicleSelectionStateProvider =
    AutoDisposeNotifierProvider<
      VehicleSelectionState,
      VehicleSelection
    >.internal(
      VehicleSelectionState.new,
      name: r'vehicleSelectionStateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$vehicleSelectionStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$VehicleSelectionState = AutoDisposeNotifier<VehicleSelection>;
String _$garageActionsHash() => r'fbbc12dec9f8c401ce2a6eac27071c0701e52b6e';

/// See also [GarageActions].
@ProviderFor(GarageActions)
final garageActionsProvider =
    AutoDisposeNotifierProvider<GarageActions, AsyncValue<void>>.internal(
      GarageActions.new,
      name: r'garageActionsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$garageActionsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GarageActions = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
