// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$auctionsHash() => r'12e82773be2274192b9c439a6e7f270e6e5637cc';

/// Auction Providers
/// Following Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
/// Provider for fetching all auctions (products with auction listing type)
///
/// Copied from [auctions].
@ProviderFor(auctions)
final auctionsProvider = AutoDisposeFutureProvider<List<ProductModel>>.internal(
  auctions,
  name: r'auctionsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$auctionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuctionsRef = AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$auctionsByCategoryHash() =>
    r'eb408082508a4ab0babda65192222785ecf09aa0';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for fetching auctions by category
///
/// Copied from [auctionsByCategory].
@ProviderFor(auctionsByCategory)
const auctionsByCategoryProvider = AuctionsByCategoryFamily();

/// Provider for fetching auctions by category
///
/// Copied from [auctionsByCategory].
class AuctionsByCategoryFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Provider for fetching auctions by category
  ///
  /// Copied from [auctionsByCategory].
  const AuctionsByCategoryFamily();

  /// Provider for fetching auctions by category
  ///
  /// Copied from [auctionsByCategory].
  AuctionsByCategoryProvider call(String category) {
    return AuctionsByCategoryProvider(category);
  }

  @override
  AuctionsByCategoryProvider getProviderOverride(
    covariant AuctionsByCategoryProvider provider,
  ) {
    return call(provider.category);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'auctionsByCategoryProvider';
}

/// Provider for fetching auctions by category
///
/// Copied from [auctionsByCategory].
class AuctionsByCategoryProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Provider for fetching auctions by category
  ///
  /// Copied from [auctionsByCategory].
  AuctionsByCategoryProvider(String category)
    : this._internal(
        (ref) => auctionsByCategory(ref as AuctionsByCategoryRef, category),
        from: auctionsByCategoryProvider,
        name: r'auctionsByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$auctionsByCategoryHash,
        dependencies: AuctionsByCategoryFamily._dependencies,
        allTransitiveDependencies:
            AuctionsByCategoryFamily._allTransitiveDependencies,
        category: category,
      );

  AuctionsByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.category,
  }) : super.internal();

  final String category;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(AuctionsByCategoryRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AuctionsByCategoryProvider._internal(
        (ref) => create(ref as AuctionsByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        category: category,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _AuctionsByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AuctionsByCategoryProvider && other.category == category;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, category.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AuctionsByCategoryRef
    on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `category` of this provider.
  String get category;
}

class _AuctionsByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with AuctionsByCategoryRef {
  _AuctionsByCategoryProviderElement(super.provider);

  @override
  String get category => (origin as AuctionsByCategoryProvider).category;
}

String _$auctionHash() => r'137fc8d36e91547ca36192623fe057aea001d2fe';

/// Provider for fetching a single auction by ID
///
/// Copied from [auction].
@ProviderFor(auction)
const auctionProvider = AuctionFamily();

/// Provider for fetching a single auction by ID
///
/// Copied from [auction].
class AuctionFamily extends Family<AsyncValue<ProductModel?>> {
  /// Provider for fetching a single auction by ID
  ///
  /// Copied from [auction].
  const AuctionFamily();

  /// Provider for fetching a single auction by ID
  ///
  /// Copied from [auction].
  AuctionProvider call(String auctionId) {
    return AuctionProvider(auctionId);
  }

  @override
  AuctionProvider getProviderOverride(covariant AuctionProvider provider) {
    return call(provider.auctionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'auctionProvider';
}

/// Provider for fetching a single auction by ID
///
/// Copied from [auction].
class AuctionProvider extends AutoDisposeFutureProvider<ProductModel?> {
  /// Provider for fetching a single auction by ID
  ///
  /// Copied from [auction].
  AuctionProvider(String auctionId)
    : this._internal(
        (ref) => auction(ref as AuctionRef, auctionId),
        from: auctionProvider,
        name: r'auctionProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$auctionHash,
        dependencies: AuctionFamily._dependencies,
        allTransitiveDependencies: AuctionFamily._allTransitiveDependencies,
        auctionId: auctionId,
      );

  AuctionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.auctionId,
  }) : super.internal();

  final String auctionId;

  @override
  Override overrideWith(
    FutureOr<ProductModel?> Function(AuctionRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AuctionProvider._internal(
        (ref) => create(ref as AuctionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        auctionId: auctionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel?> createElement() {
    return _AuctionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AuctionProvider && other.auctionId == auctionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, auctionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AuctionRef on AutoDisposeFutureProviderRef<ProductModel?> {
  /// The parameter `auctionId` of this provider.
  String get auctionId;
}

class _AuctionProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel?>
    with AuctionRef {
  _AuctionProviderElement(super.provider);

  @override
  String get auctionId => (origin as AuctionProvider).auctionId;
}

String _$auctionBidsHash() => r'a9aaea2f6a1f7e31496555b0e4d3d15457d15856';

/// Provider for fetching bids for a specific auction
///
/// Copied from [auctionBids].
@ProviderFor(auctionBids)
const auctionBidsProvider = AuctionBidsFamily();

/// Provider for fetching bids for a specific auction
///
/// Copied from [auctionBids].
class AuctionBidsFamily extends Family<AsyncValue<List<BidModel>>> {
  /// Provider for fetching bids for a specific auction
  ///
  /// Copied from [auctionBids].
  const AuctionBidsFamily();

  /// Provider for fetching bids for a specific auction
  ///
  /// Copied from [auctionBids].
  AuctionBidsProvider call(String auctionId) {
    return AuctionBidsProvider(auctionId);
  }

  @override
  AuctionBidsProvider getProviderOverride(
    covariant AuctionBidsProvider provider,
  ) {
    return call(provider.auctionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'auctionBidsProvider';
}

/// Provider for fetching bids for a specific auction
///
/// Copied from [auctionBids].
class AuctionBidsProvider extends AutoDisposeFutureProvider<List<BidModel>> {
  /// Provider for fetching bids for a specific auction
  ///
  /// Copied from [auctionBids].
  AuctionBidsProvider(String auctionId)
    : this._internal(
        (ref) => auctionBids(ref as AuctionBidsRef, auctionId),
        from: auctionBidsProvider,
        name: r'auctionBidsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$auctionBidsHash,
        dependencies: AuctionBidsFamily._dependencies,
        allTransitiveDependencies: AuctionBidsFamily._allTransitiveDependencies,
        auctionId: auctionId,
      );

  AuctionBidsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.auctionId,
  }) : super.internal();

  final String auctionId;

  @override
  Override overrideWith(
    FutureOr<List<BidModel>> Function(AuctionBidsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AuctionBidsProvider._internal(
        (ref) => create(ref as AuctionBidsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        auctionId: auctionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<BidModel>> createElement() {
    return _AuctionBidsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AuctionBidsProvider && other.auctionId == auctionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, auctionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AuctionBidsRef on AutoDisposeFutureProviderRef<List<BidModel>> {
  /// The parameter `auctionId` of this provider.
  String get auctionId;
}

class _AuctionBidsProviderElement
    extends AutoDisposeFutureProviderElement<List<BidModel>>
    with AuctionBidsRef {
  _AuctionBidsProviderElement(super.provider);

  @override
  String get auctionId => (origin as AuctionBidsProvider).auctionId;
}

String _$placeBidHash() => r'fe5b63de90f057b46194459529e5867c1aeda03e';

/// Provider for placing a bid on an auction
///
/// Copied from [placeBid].
@ProviderFor(placeBid)
const placeBidProvider = PlaceBidFamily();

/// Provider for placing a bid on an auction
///
/// Copied from [placeBid].
class PlaceBidFamily extends Family<AsyncValue<bool>> {
  /// Provider for placing a bid on an auction
  ///
  /// Copied from [placeBid].
  const PlaceBidFamily();

  /// Provider for placing a bid on an auction
  ///
  /// Copied from [placeBid].
  PlaceBidProvider call(String auctionId, double amount) {
    return PlaceBidProvider(auctionId, amount);
  }

  @override
  PlaceBidProvider getProviderOverride(covariant PlaceBidProvider provider) {
    return call(provider.auctionId, provider.amount);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'placeBidProvider';
}

/// Provider for placing a bid on an auction
///
/// Copied from [placeBid].
class PlaceBidProvider extends AutoDisposeFutureProvider<bool> {
  /// Provider for placing a bid on an auction
  ///
  /// Copied from [placeBid].
  PlaceBidProvider(String auctionId, double amount)
    : this._internal(
        (ref) => placeBid(ref as PlaceBidRef, auctionId, amount),
        from: placeBidProvider,
        name: r'placeBidProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$placeBidHash,
        dependencies: PlaceBidFamily._dependencies,
        allTransitiveDependencies: PlaceBidFamily._allTransitiveDependencies,
        auctionId: auctionId,
        amount: amount,
      );

  PlaceBidProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.auctionId,
    required this.amount,
  }) : super.internal();

  final String auctionId;
  final double amount;

  @override
  Override overrideWith(FutureOr<bool> Function(PlaceBidRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: PlaceBidProvider._internal(
        (ref) => create(ref as PlaceBidRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        auctionId: auctionId,
        amount: amount,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _PlaceBidProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PlaceBidProvider &&
        other.auctionId == auctionId &&
        other.amount == amount;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, auctionId.hashCode);
    hash = _SystemHash.combine(hash, amount.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PlaceBidRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `auctionId` of this provider.
  String get auctionId;

  /// The parameter `amount` of this provider.
  double get amount;
}

class _PlaceBidProviderElement extends AutoDisposeFutureProviderElement<bool>
    with PlaceBidRef {
  _PlaceBidProviderElement(super.provider);

  @override
  String get auctionId => (origin as PlaceBidProvider).auctionId;
  @override
  double get amount => (origin as PlaceBidProvider).amount;
}

String _$submitBidHash() => r'4595fc723af4d76fd7ffa2c46d86fb3b89fb7ea4';

/// Provider for bid submission with optimistic updates
///
/// Copied from [submitBid].
@ProviderFor(submitBid)
const submitBidProvider = SubmitBidFamily();

/// Provider for bid submission with optimistic updates
///
/// Copied from [submitBid].
class SubmitBidFamily extends Family<AsyncValue<void>> {
  /// Provider for bid submission with optimistic updates
  ///
  /// Copied from [submitBid].
  const SubmitBidFamily();

  /// Provider for bid submission with optimistic updates
  ///
  /// Copied from [submitBid].
  SubmitBidProvider call(String auctionId, double bidAmount) {
    return SubmitBidProvider(auctionId, bidAmount);
  }

  @override
  SubmitBidProvider getProviderOverride(covariant SubmitBidProvider provider) {
    return call(provider.auctionId, provider.bidAmount);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'submitBidProvider';
}

/// Provider for bid submission with optimistic updates
///
/// Copied from [submitBid].
class SubmitBidProvider extends AutoDisposeFutureProvider<void> {
  /// Provider for bid submission with optimistic updates
  ///
  /// Copied from [submitBid].
  SubmitBidProvider(String auctionId, double bidAmount)
    : this._internal(
        (ref) => submitBid(ref as SubmitBidRef, auctionId, bidAmount),
        from: submitBidProvider,
        name: r'submitBidProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$submitBidHash,
        dependencies: SubmitBidFamily._dependencies,
        allTransitiveDependencies: SubmitBidFamily._allTransitiveDependencies,
        auctionId: auctionId,
        bidAmount: bidAmount,
      );

  SubmitBidProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.auctionId,
    required this.bidAmount,
  }) : super.internal();

  final String auctionId;
  final double bidAmount;

  @override
  Override overrideWith(FutureOr<void> Function(SubmitBidRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: SubmitBidProvider._internal(
        (ref) => create(ref as SubmitBidRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        auctionId: auctionId,
        bidAmount: bidAmount,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _SubmitBidProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SubmitBidProvider &&
        other.auctionId == auctionId &&
        other.bidAmount == bidAmount;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, auctionId.hashCode);
    hash = _SystemHash.combine(hash, bidAmount.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SubmitBidRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `auctionId` of this provider.
  String get auctionId;

  /// The parameter `bidAmount` of this provider.
  double get bidAmount;
}

class _SubmitBidProviderElement extends AutoDisposeFutureProviderElement<void>
    with SubmitBidRef {
  _SubmitBidProviderElement(super.provider);

  @override
  String get auctionId => (origin as SubmitBidProvider).auctionId;
  @override
  double get bidAmount => (origin as SubmitBidProvider).bidAmount;
}

String _$auctionServiceHash() => r'd547112f716f99585eddfbde3e5cccf5a48cb237';

/// Provider for auction service
///
/// Copied from [auctionService].
@ProviderFor(auctionService)
final auctionServiceProvider = AutoDisposeProvider<AuctionService>.internal(
  auctionService,
  name: r'auctionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$auctionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuctionServiceRef = AutoDisposeProviderRef<AuctionService>;
String _$isCurrentUserHighestBidderHash() =>
    r'ba52ee722fb8debc9a31d3d3f4fdcfcd4bc7db2a';

/// Provider to check if the current user is the highest bidder
/// Forever Plan: Uses Go backend for data, SimpleAuthSystem for user info
///
/// Copied from [isCurrentUserHighestBidder].
@ProviderFor(isCurrentUserHighestBidder)
const isCurrentUserHighestBidderProvider = IsCurrentUserHighestBidderFamily();

/// Provider to check if the current user is the highest bidder
/// Forever Plan: Uses Go backend for data, SimpleAuthSystem for user info
///
/// Copied from [isCurrentUserHighestBidder].
class IsCurrentUserHighestBidderFamily extends Family<AsyncValue<bool>> {
  /// Provider to check if the current user is the highest bidder
  /// Forever Plan: Uses Go backend for data, SimpleAuthSystem for user info
  ///
  /// Copied from [isCurrentUserHighestBidder].
  const IsCurrentUserHighestBidderFamily();

  /// Provider to check if the current user is the highest bidder
  /// Forever Plan: Uses Go backend for data, SimpleAuthSystem for user info
  ///
  /// Copied from [isCurrentUserHighestBidder].
  IsCurrentUserHighestBidderProvider call(String auctionId) {
    return IsCurrentUserHighestBidderProvider(auctionId);
  }

  @override
  IsCurrentUserHighestBidderProvider getProviderOverride(
    covariant IsCurrentUserHighestBidderProvider provider,
  ) {
    return call(provider.auctionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'isCurrentUserHighestBidderProvider';
}

/// Provider to check if the current user is the highest bidder
/// Forever Plan: Uses Go backend for data, SimpleAuthSystem for user info
///
/// Copied from [isCurrentUserHighestBidder].
class IsCurrentUserHighestBidderProvider
    extends AutoDisposeFutureProvider<bool> {
  /// Provider to check if the current user is the highest bidder
  /// Forever Plan: Uses Go backend for data, SimpleAuthSystem for user info
  ///
  /// Copied from [isCurrentUserHighestBidder].
  IsCurrentUserHighestBidderProvider(String auctionId)
    : this._internal(
        (ref) => isCurrentUserHighestBidder(
          ref as IsCurrentUserHighestBidderRef,
          auctionId,
        ),
        from: isCurrentUserHighestBidderProvider,
        name: r'isCurrentUserHighestBidderProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$isCurrentUserHighestBidderHash,
        dependencies: IsCurrentUserHighestBidderFamily._dependencies,
        allTransitiveDependencies:
            IsCurrentUserHighestBidderFamily._allTransitiveDependencies,
        auctionId: auctionId,
      );

  IsCurrentUserHighestBidderProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.auctionId,
  }) : super.internal();

  final String auctionId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(IsCurrentUserHighestBidderRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: IsCurrentUserHighestBidderProvider._internal(
        (ref) => create(ref as IsCurrentUserHighestBidderRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        auctionId: auctionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _IsCurrentUserHighestBidderProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is IsCurrentUserHighestBidderProvider &&
        other.auctionId == auctionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, auctionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin IsCurrentUserHighestBidderRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `auctionId` of this provider.
  String get auctionId;
}

class _IsCurrentUserHighestBidderProviderElement
    extends AutoDisposeFutureProviderElement<bool>
    with IsCurrentUserHighestBidderRef {
  _IsCurrentUserHighestBidderProviderElement(super.provider);

  @override
  String get auctionId =>
      (origin as IsCurrentUserHighestBidderProvider).auctionId;
}

String _$auctionTimeRemainingHash() =>
    r'30fbf22051a77e12763f4c9287c5055b6060580d';

/// Provider that tracks time remaining for an auction
///
/// Copied from [auctionTimeRemaining].
@ProviderFor(auctionTimeRemaining)
const auctionTimeRemainingProvider = AuctionTimeRemainingFamily();

/// Provider that tracks time remaining for an auction
///
/// Copied from [auctionTimeRemaining].
class AuctionTimeRemainingFamily extends Family<AsyncValue<Duration>> {
  /// Provider that tracks time remaining for an auction
  ///
  /// Copied from [auctionTimeRemaining].
  const AuctionTimeRemainingFamily();

  /// Provider that tracks time remaining for an auction
  ///
  /// Copied from [auctionTimeRemaining].
  AuctionTimeRemainingProvider call(String auctionId) {
    return AuctionTimeRemainingProvider(auctionId);
  }

  @override
  AuctionTimeRemainingProvider getProviderOverride(
    covariant AuctionTimeRemainingProvider provider,
  ) {
    return call(provider.auctionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'auctionTimeRemainingProvider';
}

/// Provider that tracks time remaining for an auction
///
/// Copied from [auctionTimeRemaining].
class AuctionTimeRemainingProvider extends AutoDisposeStreamProvider<Duration> {
  /// Provider that tracks time remaining for an auction
  ///
  /// Copied from [auctionTimeRemaining].
  AuctionTimeRemainingProvider(String auctionId)
    : this._internal(
        (ref) =>
            auctionTimeRemaining(ref as AuctionTimeRemainingRef, auctionId),
        from: auctionTimeRemainingProvider,
        name: r'auctionTimeRemainingProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$auctionTimeRemainingHash,
        dependencies: AuctionTimeRemainingFamily._dependencies,
        allTransitiveDependencies:
            AuctionTimeRemainingFamily._allTransitiveDependencies,
        auctionId: auctionId,
      );

  AuctionTimeRemainingProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.auctionId,
  }) : super.internal();

  final String auctionId;

  @override
  Override overrideWith(
    Stream<Duration> Function(AuctionTimeRemainingRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AuctionTimeRemainingProvider._internal(
        (ref) => create(ref as AuctionTimeRemainingRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        auctionId: auctionId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<Duration> createElement() {
    return _AuctionTimeRemainingProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AuctionTimeRemainingProvider &&
        other.auctionId == auctionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, auctionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AuctionTimeRemainingRef on AutoDisposeStreamProviderRef<Duration> {
  /// The parameter `auctionId` of this provider.
  String get auctionId;
}

class _AuctionTimeRemainingProviderElement
    extends AutoDisposeStreamProviderElement<Duration>
    with AuctionTimeRemainingRef {
  _AuctionTimeRemainingProviderElement(super.provider);

  @override
  String get auctionId => (origin as AuctionTimeRemainingProvider).auctionId;
}

String _$auctionStatsHash() => r'726cb15e6202bf82af3b6b6b13a068b0d74cd5f7';

/// Provider for auction stats
///
/// Copied from [auctionStats].
@ProviderFor(auctionStats)
const auctionStatsProvider = AuctionStatsFamily();

/// Provider for auction stats
///
/// Copied from [auctionStats].
class AuctionStatsFamily extends Family<AsyncValue<AuctionStats>> {
  /// Provider for auction stats
  ///
  /// Copied from [auctionStats].
  const AuctionStatsFamily();

  /// Provider for auction stats
  ///
  /// Copied from [auctionStats].
  AuctionStatsProvider call(String auctionId) {
    return AuctionStatsProvider(auctionId);
  }

  @override
  AuctionStatsProvider getProviderOverride(
    covariant AuctionStatsProvider provider,
  ) {
    return call(provider.auctionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'auctionStatsProvider';
}

/// Provider for auction stats
///
/// Copied from [auctionStats].
class AuctionStatsProvider extends AutoDisposeFutureProvider<AuctionStats> {
  /// Provider for auction stats
  ///
  /// Copied from [auctionStats].
  AuctionStatsProvider(String auctionId)
    : this._internal(
        (ref) => auctionStats(ref as AuctionStatsRef, auctionId),
        from: auctionStatsProvider,
        name: r'auctionStatsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$auctionStatsHash,
        dependencies: AuctionStatsFamily._dependencies,
        allTransitiveDependencies:
            AuctionStatsFamily._allTransitiveDependencies,
        auctionId: auctionId,
      );

  AuctionStatsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.auctionId,
  }) : super.internal();

  final String auctionId;

  @override
  Override overrideWith(
    FutureOr<AuctionStats> Function(AuctionStatsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AuctionStatsProvider._internal(
        (ref) => create(ref as AuctionStatsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        auctionId: auctionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<AuctionStats> createElement() {
    return _AuctionStatsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AuctionStatsProvider && other.auctionId == auctionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, auctionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AuctionStatsRef on AutoDisposeFutureProviderRef<AuctionStats> {
  /// The parameter `auctionId` of this provider.
  String get auctionId;
}

class _AuctionStatsProviderElement
    extends AutoDisposeFutureProviderElement<AuctionStats>
    with AuctionStatsRef {
  _AuctionStatsProviderElement(super.provider);

  @override
  String get auctionId => (origin as AuctionStatsProvider).auctionId;
}

String _$endExpiredAuctionsHash() =>
    r'b0cf2afc47aca68307e1451237685add89145a88';

/// Provider for ending auctions that have expired
///
/// Copied from [endExpiredAuctions].
@ProviderFor(endExpiredAuctions)
final endExpiredAuctionsProvider = AutoDisposeFutureProvider<bool>.internal(
  endExpiredAuctions,
  name: r'endExpiredAuctionsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$endExpiredAuctionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EndExpiredAuctionsRef = AutoDisposeFutureProviderRef<bool>;
String _$highestBidHash() => r'0b5500f079737bdb1d4313d1226a45fa393594a9';

/// Provider for getting the highest bid for an auction
///
/// Copied from [highestBid].
@ProviderFor(highestBid)
const highestBidProvider = HighestBidFamily();

/// Provider for getting the highest bid for an auction
///
/// Copied from [highestBid].
class HighestBidFamily extends Family<AsyncValue<BidModel?>> {
  /// Provider for getting the highest bid for an auction
  ///
  /// Copied from [highestBid].
  const HighestBidFamily();

  /// Provider for getting the highest bid for an auction
  ///
  /// Copied from [highestBid].
  HighestBidProvider call(String auctionId) {
    return HighestBidProvider(auctionId);
  }

  @override
  HighestBidProvider getProviderOverride(
    covariant HighestBidProvider provider,
  ) {
    return call(provider.auctionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'highestBidProvider';
}

/// Provider for getting the highest bid for an auction
///
/// Copied from [highestBid].
class HighestBidProvider extends AutoDisposeFutureProvider<BidModel?> {
  /// Provider for getting the highest bid for an auction
  ///
  /// Copied from [highestBid].
  HighestBidProvider(String auctionId)
    : this._internal(
        (ref) => highestBid(ref as HighestBidRef, auctionId),
        from: highestBidProvider,
        name: r'highestBidProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$highestBidHash,
        dependencies: HighestBidFamily._dependencies,
        allTransitiveDependencies: HighestBidFamily._allTransitiveDependencies,
        auctionId: auctionId,
      );

  HighestBidProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.auctionId,
  }) : super.internal();

  final String auctionId;

  @override
  Override overrideWith(
    FutureOr<BidModel?> Function(HighestBidRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: HighestBidProvider._internal(
        (ref) => create(ref as HighestBidRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        auctionId: auctionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<BidModel?> createElement() {
    return _HighestBidProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is HighestBidProvider && other.auctionId == auctionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, auctionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin HighestBidRef on AutoDisposeFutureProviderRef<BidModel?> {
  /// The parameter `auctionId` of this provider.
  String get auctionId;
}

class _HighestBidProviderElement
    extends AutoDisposeFutureProviderElement<BidModel?>
    with HighestBidRef {
  _HighestBidProviderElement(super.provider);

  @override
  String get auctionId => (origin as HighestBidProvider).auctionId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
