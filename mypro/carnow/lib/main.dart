// =============================================================================
// تطبيق كار ناو - نقطة الدخول الرئيسية
// CarNow Application Entry Point
// =============================================================================
//
// هذا الملف هو نقطة الدخول الرئيسية لتطبيق كار ناو، وهو سوق إلكتروني لقطع غيار السيارات
// يدعم التطبيق اللغتين العربية والإنجليزية ويستخدم أحدث تقنيات Flutter
//
// الميزات الرئيسية:
// - سوق إلكتروني شامل لقطع غيار السيارات
// - نظام مصادقة متقدم مع دعم وسائل التواصل الاجتماعي
// - إدارة المحفظة والمعاملات المالية
// - نظام إدارة المخزون والمنتجات
// - دعم كامل للغة العربية والإنجليزية
// - تحسينات الأداء والاستقرار
//
// تم إعادة هيكلة هذا الملف لتحسين الأداء والصيانة
// تم نقل منطق التهيئة الأساسي إلى وحدات منفصلة لتنظيم أفضل

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'dart:async';

import 'core/utils/unified_logger.dart';
import 'core/app/carnow_app.dart';
import 'core/app/simple_app_initialization.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'core/config/app_config.dart';
import 'core/config/env_config.dart';
import 'core/services/backend_resilience_initializer.dart';
import 'core/app/performance_main_thread_fix.dart';
import 'core/performance/startup_optimizer.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

/// الدالة الرئيسية لتطبيق كار ناو - محسنة للإقلاع السريع
/// تم نقل منطق التهيئة المعقد إلى [startup_optimizer.dart]
/// لضمان أسرع وقت ممكن للرسم الأول (Time to First Paint).

Future<void> main() async {
  // --- STARTUP PHASE 1: SYNCHRONOUS & CRITICAL ---
  //
  // All calls here must be synchronous, lightweight, and critical for the first frame.
  //
  WidgetsFlutterBinding.ensureInitialized();

  // Set up basic logging first
  Logger.root.level = kDebugMode ? Level.ALL : Level.INFO;

  // Apply performance fixes early to prevent frame skipping
  PerformanceMainThreadFix.applyAllFixes();

  // Initialize startup optimizer for better performance
  await StartupOptimizer.optimizeStartup();

  // Load environment variables (e.g., Google OAuth client IDs)
  try {
    await dotenv.load();
  } catch (_) {
    // In debug mode we log a warning, in release we fail silently
    if (kDebugMode) {
      UnifiedLogger.warning('Environment not loaded, falling back to hard-coded IDs');
    }
  }

  // Initialize EnvConfig to set _isLoaded flag and allow access without warnings
  await EnvConfig.initialize();
  
  // CRITICAL: Initialize essential services BEFORE running the app
  try {
    await SimpleAppInitialization.initialize();
  } catch (e, stackTrace) {
    Logger.root.severe('Failed to initialize app', e, stackTrace);
    // Still try to run the app to show error UI
  }

  // Initialize global app configuration
  await AppConfig.initialize();

  // Initialize Sentry only if DSN is properly configured
  final dsn = dotenv.env['SENTRY_DSN'] ?? '';
  if (dsn.isNotEmpty && !dsn.contains('your-sentry-dsn')) {
    await SentryFlutter.init((options) {
      options.dsn = dsn;
      options.tracesSampleRate = 1.0;
      options.environment = kDebugMode ? 'development' : 'production';
    },
    appRunner: () => runApp(
      const ProviderScope(
        child: CarNowApp(),
      ),
    ));
  } else {
    if (kDebugMode) {
      Logger.root.info('Sentry not initialized: Missing or invalid SENTRY_DSN in .env');
    }
    runApp(
      const ProviderScope(
        child: CarNowApp(),
      ),
    );
  }

  // Schedule non-critical initialization after first frame
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _postFrameInitialization();
  });
}

/// Post-frame initialization for non-critical services
Future<void> _postFrameInitialization() async {
  try {
    // Print config summary after first frame to avoid blocking
    if (kDebugMode) {
      EnvConfig.printConfigSummary();
    }

    // Initialize Backend Resilience System (includes CarnowApiService)
    await BackendResilienceInitializer.initialize();
    
    // Log backend resilience status
    final status = BackendResilienceInitializer.getSystemStatus();
    Logger.root.info('Backend Resilience Status: $status');
    
  } catch (error, stackTrace) {
    Logger.root.severe(
      'Post-frame initialization failed',
      error,
      stackTrace,
    );
  }
}
