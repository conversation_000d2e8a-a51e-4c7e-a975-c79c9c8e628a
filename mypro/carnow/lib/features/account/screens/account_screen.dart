import 'package:flutter/foundation.dart'; // For kDebugMode
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart'; // For HookConsumerWidget
import 'package:logging/logging.dart'; // For logging
import '../../../core/utils/unified_logger.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/theme/app_theme.dart';
import '../models/user_model.dart';
import '../../../l10n/app_localizations.dart'; // Import for translations

final _logger = Logger('AccountScreen');

/// شاشة حساب المستخدم
///
/// تعرض هذه الشاشة لوحة التحكم الخاصة بالمستخدم، حيث يمكنه الاطلاع على
/// بياناته الشخصية، إدارة حالة حسابه كبائع، وتحديث معلوماته.
/// تتضمن الشاشة أيضاً أدوات تشخيصية لتسجيل حالة المصادقة وبيانات المستخدم
/// للمساعدة في تصحيح الأخطاء.
class AccountScreen extends ConsumerStatefulWidget {
  const AccountScreen({super.key});

  @override
  ConsumerState<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends ConsumerState<AccountScreen> {
  final _usernameController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _websiteController = TextEditingController();

  UserModel? get _user {
    final currentUser = ref.watch(currentUserProvider);
    return currentUser;
  }

  @override
  void initState() {
    super.initState();
    if (_user != null) {
      _usernameController.text = _user!.name ?? '';
      _fullNameController.text = _user!.name ?? '';
      _websiteController.text = ''; // Company website not available in UserModel
    }
    // تحديث الملف الشخصي عند فتح الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshUserProfile();
      // إضافة سجلات تشخيصية إضافية
      _logCurrentAuthState();
    });
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _fullNameController.dispose();
    _websiteController.dispose();
    super.dispose();
  }

  Future<void> _refreshUserProfile() async {
    try {
      _logger.info('Refreshing user profile from account screen');
      // إعادة تحديث الموفر بدلاً من استدعاء طريقة غير موجودة
      ref
        ..invalidate(simpleSupabaseAuthProvider)
        // إعادة تحديث مزود المستخدم الحالي أيضاً
        ..invalidate(currentUserProvider);

      // انتظار قصير ثم تسجيل الحالة مرة أخرى
      await Future<void>.delayed(const Duration(milliseconds: 500));
      _logCurrentAuthState();

      _logger.info('User profile refreshed successfully');
    } catch (e) {
      _logger.severe('Error refreshing user profile: $e');
    }
  }

  void _logCurrentAuthState() {
    try {
      final authState = ref.read(simpleSupabaseAuthProvider);
      final currentUser = ref.read(currentUserProvider);
      final authEmail = currentUser?.email;

      _logger
        ..info('=== تشخيص حالة المصادقة ===')
        ..info('حالة المصادقة: ${authState.toString()}')
        ..info(
          'البريد الإلكتروني للمستخدم (من المصادقة): ${authEmail ?? 'null'}',
        )
        ..info('=== تشخيص بيانات المستخدم ===')
        ..info('User ID: ${currentUser?.id ?? 'null'}')
        ..info('User Name: ${currentUser?.name ?? 'null'}')
        ..info('User Role: N/A') // Role not available in unified User model
        ..info('Is Approved: N/A') // Approval not available in unified User model
        ..info('Is Seller Requested: N/A') // Seller status not available in unified User model
        ..info('Seller Status: N/A')
        ..info('Can Request to be Seller: N/A')
        ..info('=== نهاية التشخيص ===');
    } catch (e) {
      _logger.severe('خطأ في تشخيص حالة المصادقة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    final currentUser = ref.watch(currentUserProvider);
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    if (!isAuthenticated) {
      return Scaffold(
        appBar: AppBar(
          title: Text(l10n.account),
          automaticallyImplyLeading: false,
          centerTitle: true,
          backgroundColor: theme.scaffoldBackgroundColor,
          elevation: 0,
        ),
        body: const Center(
          child: Text('غير مصادق عليه'),
        ),
      );
    }

    return _buildAccountContent(context, ref, currentUser);
  }

  Widget _buildAccountContent(
    BuildContext context,
    WidgetRef ref,
    UserModel? user,
  ) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // Note: Seller functionality not available in unified User model
    // These features will need to be implemented via API calls to Go backend
    final canRequestSeller = false; // Placeholder - implement via API

    // تشخيص إضافي في build - سيظهر في اللوغات
    UnifiedLogger.debug(
      'ACCOUNT DEBUG: User=${user?.name}, Email=${user?.email}',
      tag: 'AccountScreen',
    );
    _logger.info('=== Account Screen Build ===');
    _logger.info('User: ${user?.name} (${user?.email})');
    _logger.info('Role: N/A'); // Role not available in unified User model
    _logger.info('isApproved: N/A'); // Approval not available in unified User model
    _logger.info('isSellerRequested: N/A'); // Seller status not available
    _logger.info('canRequestSeller: $canRequestSeller');
    _logger.info('=========================');

    // Define a ListTileTheme for consistent styling
    final listTileTheme = ListTileThemeData(
      iconColor: theme.colorScheme.onSurfaceVariant,
      textColor: theme.colorScheme.onSurface,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingL),
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.account),
        automaticallyImplyLeading: false,
        centerTitle: true, // Center the title
        backgroundColor: theme.scaffoldBackgroundColor, // Match background
        elevation: 0, // Remove shadow
      ),
      // Apply ListTileTheme to the entire ListView
      body: ListTileTheme(
        data: listTileTheme, // Use data parameter here
        child: ListView(
          children: [
            const _AccountHeader(), // Get user from unified auth provider
            const SizedBox(height: AppTheme.spacingM),

            // مربع تشخيص واضح - يظهر دائماً
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red, width: 4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.bug_report, color: Colors.red[800], size: 24),
                      const SizedBox(width: 8),
                      Text(
                        'DEBUG - معلومات البائع',
                        style: TextStyle(
                          color: Colors.red[800],
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildDebugRow('الدور', 'N/A'), // Role not available in unified User model
                  _buildDebugRow(
                    'معتمد',
                    'N/A', // Approval not available in unified User model
                    isImportant: true,
                  ),
                  _buildDebugRow(
                    'طلب بيع',
                    'N/A', // Seller request not available in unified User model
                  ),
                  _buildDebugRow(
                    'حالة البائع',
                    'N/A', // Seller status not available in unified User model
                    color: Colors.red[700],
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),

            // عرض الأقسام حسب حالة البائع
            // Note: Seller functionality disabled until implemented via Go backend API
            // Showing buyer sections only for now
            // للمشتري العادي - عرض نشاط التسوق
            _SectionHeader(title: l10n.shoppingActivitySection),
            _AccountMenuItem(
              title: l10n.myOrders,
              subtitle: l10n.myOrdersSubtitle,
              icon: Icons.shopping_bag_outlined,
              onTap: () {
                context.push('/orders');
              },
            ),
            _AccountMenuItem(
              title: l10n.myListings,
              subtitle: l10n.myListingsSubtitle,
              icon: Icons.inventory_2_outlined,
              onTap: () {
                context.push('/my-listings');
              },
            ),
            _AccountMenuItem(
              title: l10n.myCars,
              icon: Icons.directions_car_outlined,
              onTap: () {
                context.push('/garage');
              },
            ),
            _AccountMenuItem(
              title: l10n.messages,
              subtitle: l10n.messagesSubtitle,
              icon: Icons.message_outlined,
              onTap: () {
                context.push('/messages');
              },
            ),

            // --- Seller Section ---
            const Divider(
              height: AppTheme.spacingXL,
              indent: AppTheme.spacingL,
              endIndent: AppTheme.spacingL,
            ),
            const _SectionHeader(title: 'البيع'),

            // عرض خيارات مختلفة حسب حالة البائع
            // Note: Seller functionality will be implemented via Go backend API
            if (canRequestSeller) ...[
              // يمكن طلب أن يصبح بائع
              _AccountMenuItem(
                title: l10n.becomeASeller,
                subtitle: l10n.becomeASellerSubtitle,
                icon: Icons.store_outlined,
                onTap: () {
                  context.push('/seller/subscription-request');
                },
              ),
              _AccountMenuItem(
                title: 'معلومات البيع',
                subtitle: 'تعرف على شروط وأحكام البيع',
                icon: Icons.info_outline,
                onTap: () {
                  context.push('/seller-info');
                },
              ),
            ],

            // --- Settings & Support Section ---
            const Divider(
              height: AppTheme.spacingXL,
              indent: AppTheme.spacingL,
              endIndent: AppTheme.spacingL,
            ),
            _SectionHeader(title: l10n.settingsAndSupport),

            // Debug section for authentication issues (only in debug mode)
            if (kDebugMode) ...[
              const _SectionHeader(title: 'تشخيص المصادقة'),
            ],

            _AccountMenuItem(
              title: l10n.settings,
              subtitle: l10n.accountSettings,
              icon: Icons.settings_outlined,
              onTap: () {
                _logger.info('Account Settings tapped');
                // Navigate to personal information screen
                context.push('/settings');
              },
            ),
            _AccountMenuItem(
              title: l10n.personalInformation,
              subtitle: l10n.editYourProfileDetails,
              icon: Icons.person_outline,
              onTap: () {
                _logger.info('Personal Information tapped');
                context.push(
                  '/account/profile/edit',
                ); // Navigate to new edit route
              },
            ),
            _AccountMenuItem(
              title: l10n.support,
              subtitle: l10n.getHelpAndContactSupport,
              icon: Icons.help_outline,
              onTap: () {
                context.push('/support');
              },
            ),
            _AccountMenuItem(
              title: l10n.logout,
              icon: Icons.logout,
              onTap: () async {
                _logger.info('Logout tapped');
                // Use unified auth system notifier for signOut
                await ref.read(simpleSupabaseAuthProvider.notifier).signOut();
                // Navigation is now handled globally by the auth system
                // No need for manual navigation here
              },
              isDestructive: true,
            ),

            // --- Debug Section (Conditional) ---
            if (kDebugMode) ...[
              const Divider(
                height: AppTheme.spacingXL,
                indent: AppTheme.spacingL,
                endIndent: AppTheme.spacingL,
              ),
              const _SectionHeader(title: 'DEBUG'),
              _AccountMenuItem(
                title: 'إعادة تحديث حالة المصادقة',
                subtitle: 'لاختبار التشخيص',
                icon: Icons.refresh,
                onTap: () async {
                  await _refreshUserProfile();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'تم إعادة تحديث حالة المصادقة - تحقق من السجلات',
                        ),
                      ),
                    );
                  }
                },
                iconColor: Theme.of(context).colorScheme.secondary,
                textColor: Theme.of(context).colorScheme.secondary,
              ),
              _AccountMenuItem(
                title: 'إعادة تسجيل الدخول بـ Google',
                subtitle: 'لاختبار المصادقة',
                icon: Icons.g_mobiledata,
                onTap: () async {
                  try {
                    await ref
                        .read(simpleSupabaseAuthProvider.notifier)
                        .signInWithGoogle();
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('تم تسجيل الدخول بنجاح')),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('خطأ في تسجيل الدخول: $e')),
                      );
                    }
                  }
                },
                iconColor: Colors.green,
                textColor: Colors.green,
              ),
            ],

            const SizedBox(height: AppTheme.spacingL),
          ],
        ),
      ),
    );
  }





  /// بناء صف تشخيص
  Widget _buildDebugRow(
    String label,
    String value, {
    bool isImportant = false,
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: color ?? Colors.red[700],
                fontSize: 16,
                fontWeight: isImportant ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// --- Account Header Widget ---
class _AccountHeader extends ConsumerWidget {
  const _AccountHeader();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    // Get both auth state and user from unified provider
    final authState = ref.watch(simpleSupabaseAuthProvider);

    // Handle different auth states
    if (authState is SimpleAuthStateAuthenticated) {
      // User is authenticated, show their data
      final user = authState.user;
      final email = user.email ?? '';
      final userName = user.name ?? 'مستخدم';
      final imageUrl = user.profileImageUrl;

      _logger
        ..info('Building _AccountHeader with authenticated user: $userName')
        ..info('User email: $email')
        ..info('Profile image: $imageUrl');

      return Container(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Row(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: theme.colorScheme.primaryContainer,
              backgroundImage: imageUrl != null ? NetworkImage(imageUrl) : null,
              child: imageUrl == null
                  ? Icon(
                      Icons.person,
                      size: 40,
                      color: theme.colorScheme.onPrimaryContainer,
                    )
                  : null,
            ),
            const SizedBox(width: AppTheme.spacingL),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userName,
                    style: theme.textTheme.titleLarge,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppTheme.spacingXS),
                  Text(
                    email,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    } else if (authState is SimpleAuthStateLoading) {
      return Container(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        alignment: Alignment.center,
        child: const CircularProgressIndicator(),
      );
    } else if (authState is SimpleAuthStateError) {
      return Container(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        alignment: Alignment.center,
        child: Column(
          children: [
            Icon(Icons.error_outline, color: theme.colorScheme.error),
            Text(
              'خطأ في تحميل بيانات المستخدم',
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      );
    } else {
      // Unauthenticated or other states
      return Container(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Row(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: theme.colorScheme.primaryContainer,
              child: Icon(
                Icons.person,
                size: 40,
                color: theme.colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(width: AppTheme.spacingL),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.guestUser,
                    style: theme.textTheme.titleLarge,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppTheme.spacingXS),
                  Text(
                    'يرجى تسجيل الدخول للوصول إلى حسابك',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            FilledButton(
              onPressed: () => context.push('/login'),
              child: const Text('تسجيل الدخول'),
            ),
          ],
        ),
      );
    }
  }
}

// --- Section Header Widget ---
class _SectionHeader extends StatelessWidget {
  const _SectionHeader({required this.title});
  final String title;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.only(
        left: AppTheme.spacingL,
        right: AppTheme.spacingL,
        top: AppTheme.spacingS, // Reduced top padding
        bottom: AppTheme.spacingXS, // Reduced bottom padding
      ),
      child: Text(
        title.toUpperCase(), // Uppercase for section titles
        style: theme.textTheme.labelMedium?.copyWith(
          // Use labelMedium
          color: theme.colorScheme.primary, // Use primary color
          fontWeight: FontWeight.bold,
          letterSpacing: 0.8, // Add letter spacing
        ),
      ),
    );
  }
}

// --- Menu Item Widget ---
// Modified to accept title/subtitle strings directly
class _AccountMenuItem extends StatelessWidget {
  const _AccountMenuItem({
    required this.title,
    required this.icon,
    required this.onTap,
    this.subtitle,
    this.isDestructive = false,
    this.iconColor,
    this.textColor,
  });

  final String title;
  final String? subtitle; // Optional subtitle
  final IconData icon;
  final VoidCallback onTap;
  final bool isDestructive;
  final Color? iconColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final effectiveIconColor = isDestructive
        ? theme.colorScheme.error
        : iconColor ?? theme.colorScheme.onSurfaceVariant;

    final effectiveTextColor = isDestructive
        ? theme.colorScheme.error
        : textColor ?? theme.colorScheme.onSurface;

    return ListTile(
      leading: Icon(icon, color: effectiveIconColor),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(color: effectiveTextColor),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }
}
