// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentUserStreamHash() => r'aa683b510d688e4beaf3941a28dd7fbfffb19bff';

/// Compatibility provider that provides UserModel stream from SimpleAuthState
///
/// Copied from [currentUserStream].
@ProviderFor(currentUserStream)
final currentUserStreamProvider =
    AutoDisposeStreamProvider<UserModel?>.internal(
      currentUserStream,
      name: r'currentUserStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentUserStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserStreamRef = AutoDisposeStreamProviderRef<UserModel?>;
String _$currentUserHash() => r'd06b1e3852958d0b2df4ab31281f8e603effd93f';

/// Provider for the current user as UserModel (compatibility)
///
/// Copied from [currentUser].
@ProviderFor(currentUser)
final currentUserProvider = AutoDisposeProvider<UserModel?>.internal(
  currentUser,
  name: r'currentUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserRef = AutoDisposeProviderRef<UserModel?>;
String _$isAuthenticatedHash() => r'0ae0478618b7956f216187165c8b582de3f02355';

/// Provider for authentication status (compatibility)
///
/// Copied from [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$nativeGoogleAuthNotifierHash() =>
    r'2e79c43db9a27f15fb5006bc450d46fa2d8e5a9b';

/// See also [NativeGoogleAuthNotifier].
@ProviderFor(NativeGoogleAuthNotifier)
final nativeGoogleAuthNotifierProvider =
    AutoDisposeStreamNotifierProvider<
      NativeGoogleAuthNotifier,
      UserModel?
    >.internal(
      NativeGoogleAuthNotifier.new,
      name: r'nativeGoogleAuthNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$nativeGoogleAuthNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NativeGoogleAuthNotifier = AutoDisposeStreamNotifier<UserModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
