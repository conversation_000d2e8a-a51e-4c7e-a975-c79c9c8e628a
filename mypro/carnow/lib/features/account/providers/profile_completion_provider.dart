import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/providers/users_provider.dart';
import '../../account/models/user_model.dart';

final _logger = Logger('ProfileCompletionProvider');

/// التحقق من اكتمال البيانات الشخصية للمستخدم
enum ProfileCompletionStatus {
  /// غير مسجل الدخول
  notLoggedIn,

  /// اكتملت البيانات الشخصية
  complete,

  /// البيانات الشخصية غير مكتملة
  incomplete,

  /// جاري التحميل
  loading,

  /// حدث خطأ
  error,
}

/// مزود للتحقق من اكتمال البيانات الشخصية للمستخدم
final profileCompletionProvider = Provider<ProfileCompletionStatus>((ref) {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);

  // التحقق إذا كان المستخدم مسجل الدخول
  if (!isAuthenticated) {
    return ProfileCompletionStatus.notLoggedIn;
  }

  // الحصول على بيانات المستخدم الكاملة
  final userProfileAsync = ref.watch(currentUserStreamProvider);

  return userProfileAsync.when(
    data: (userData) {
      // التحقق من اكتمال البيانات الأساسية
      final isComplete = _isProfileComplete(userData);
      return isComplete
          ? ProfileCompletionStatus.complete
          : ProfileCompletionStatus.incomplete;
    },
    loading: () => ProfileCompletionStatus.loading,
    error: (_, stackTrace) => ProfileCompletionStatus.error,
  );
});

/// التحقق من اكتمال البيانات الشخصية
bool _isProfileComplete(UserModel? user) {
  if (user == null) return false;

  // التحقق من البيانات الأساسية
  final hasName = user.name?.isNotEmpty ?? false;
  final hasEmail = user.email?.isNotEmpty ?? false;
  final hasPhone = user.phone?.isNotEmpty ?? false;
  final hasCity = user.cityId != null;

  _logger.info(
    'Profile completion check - Name: $hasName, Email: $hasEmail, Phone: $hasPhone, '
    'City set: $hasCity',
  );

  // يجب توفر الاسم والإيميل على الأقل
  // رقم الهاتف والمدينة اختياريان لمستخدمي OAuth لتقليل العوائق
  return hasName && hasEmail;
}

/// مزود للإشارة إلى ما إذا كان يجب توجيه المستخدم إلى صفحة إكمال البيانات
final shouldRedirectToProfileCompletion = Provider<bool>((ref) {
  final completionStatus = ref.watch(profileCompletionProvider);
  return completionStatus == ProfileCompletionStatus.incomplete;
});
