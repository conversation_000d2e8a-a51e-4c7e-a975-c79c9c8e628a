import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../models/user_model.dart';

part 'auth_providers.g.dart';

@riverpod
class NativeGoogleAuthNotifier extends _$NativeGoogleAuthNotifier {
  @override
  Stream<UserModel?> build() {
    // Use SimpleSupabaseAuthProvider for auth state changes
    final authState = ref.watch(simpleSupabaseAuthProvider);

    // Convert SimpleAuthState to UserModel stream
    if (authState is SimpleAuthStateAuthenticated) {
      return Stream.value(authState.user);
    }
    return Stream.value(null);
  }

  Future<void> signInWithGoogle() async {
    // The loading and error states for this specific operation should be
    // handled in the UI layer where this method is called, for example, by
    // using a separate local state variable (e.g., a Hook useState(false)
    // for isLoading) or another provider if the operation's state needs to
    // be more globally accessible.
    try {
      final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
      await authSystem.signInWithGoogle();
      // The user state will automatically update via the auth state stream
      // in build().
    } catch (e) {
      // Rethrow the error to be caught in the UI or calling function.
      // The UI can then display an appropriate error message.
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
      await authSystem.signOut();
      // The user state will automatically update to null via the
      // auth system.
    } catch (e) {
      rethrow;
    }
  }
}

// =============================================================================
// COMPATIBILITY PROVIDERS - Bridge between old and new auth systems
// =============================================================================

/// Compatibility provider that provides UserModel stream from SimpleAuthState
@riverpod
Stream<UserModel?> currentUserStream(Ref ref) async* {
  final authState = ref.watch(simpleSupabaseAuthProvider);

  // Handle different SimpleAuthState types
  if (authState is SimpleAuthStateAuthenticated) {
    yield authState.user;
  } else {
    yield null;
  }
}

/// Provider for the current user as UserModel (compatibility)
@riverpod
UserModel? currentUser(Ref ref) {
  final userAsync = ref.watch(currentUserStreamProvider);
  return userAsync.value;
}

/// Provider for authentication status (compatibility)
@riverpod
bool isAuthenticated(Ref ref) {
  final authState = ref.watch(simpleSupabaseAuthProvider);
  return authState is SimpleAuthStateAuthenticated;
}
