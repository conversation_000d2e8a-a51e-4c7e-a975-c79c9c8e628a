import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/utils/date_formatter.dart';
import '../models/bid_model.dart';

/// Widget for displaying individual bid information
class BidCardWidget extends ConsumerWidget {
  const BidCardWidget({
    required this.bid,
    super.key,
    this.isHighestBid = false,
    this.showUserInfo = true,
    this.onTap,
    this.isCurrentUser = false,
  });
  final BidModel bid;
  final bool isHighestBid;
  final bool showUserInfo;
  final VoidCallback? onTap;
  final bool isCurrentUser;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final authState = ref.watch(simpleSupabaseAuthProvider);
    final currentUserId = authState is SimpleAuthStateAuthenticated ? authState.user.id : null;
    final isOwnBid = currentUserId == bid.userId;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      elevation: isHighestBid ? 4 : 1,
      color: isHighestBid
          ? theme.colorScheme.primaryContainer
          : isOwnBid
          ? theme.colorScheme.secondaryContainer
          : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Bid amount and status
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '${bid.amount?.toStringAsFixed(2) ?? '00'} د.ل',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: isHighestBid
                                ? FontWeight.bold
                                : FontWeight.w600,
                            color: isHighestBid
                                ? theme.colorScheme.primary
                                : null,
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (isHighestBid)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              'الأعلى',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        if (isOwnBid && !isHighestBid)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.secondary,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              'عطائك',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.onSecondary,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    if (bid.createdAt != null)
                      Text(
                        'تم في: ${DateFormatter.formatDateTime(bid.createdAt!)}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.textTheme.bodySmall?.color?.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              // User info section
              if (showUserInfo)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (isOwnBid)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Icon(
                              Icons.person,
                              size: 16,
                              color: theme.colorScheme.primary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'أنت',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        )
                      else
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Icon(
                              Icons.person_outline,
                              size: 16,
                              color: theme.textTheme.bodySmall?.color
                                  ?.withAlpha((0.7 * 255).toInt()),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'مزايد آخر',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.textTheme.bodySmall?.color
                                    ?.withAlpha((0.7 * 255).toInt()),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),

              // Action icon
              if (onTap != null)
                Icon(
                  Icons.chevron_right,
                  color: theme.textTheme.bodySmall?.color?.withValues(
                    alpha: 0.5,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Compact version of bid card for lists
class CompactBidCard extends ConsumerWidget {
  const CompactBidCard({
    required this.bid,
    super.key,
    this.isHighestBid = false,
    this.onTap,
  });
  final BidModel bid;
  final bool isHighestBid;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final authState = ref.watch(simpleSupabaseAuthProvider);
    final currentUserId = authState is SimpleAuthStateAuthenticated ? authState.user.id : null;
    final isOwnBid = currentUserId == bid.userId;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        color: isHighestBid
            ? theme.colorScheme.primaryContainer.withAlpha((0.5 * 255).toInt())
            : isOwnBid
            ? theme.colorScheme.secondaryContainer.withAlpha(
                (0.5 * 255).toInt(),
              )
            : theme.colorScheme.surfaceContainerHighest.withAlpha(
                (0.3 * 255).toInt(),
              ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: onTap,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  Text(
                    '${bid.amount?.toStringAsFixed(2) ?? '00'} د.ل',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: isHighestBid
                          ? FontWeight.bold
                          : FontWeight.w500,
                      color: isHighestBid ? theme.colorScheme.primary : null,
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (isHighestBid)
                    Icon(
                      Icons.star,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                  if (isOwnBid && !isHighestBid)
                    Icon(
                      Icons.person,
                      size: 14,
                      color: theme.colorScheme.secondary,
                    ),
                ],
              ),
            ),
            if (bid.createdAt != null)
              Text(
                DateFormatter.timeAgo(bid.createdAt!),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.textTheme.bodySmall?.color?.withValues(
                    alpha: 0.7,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Widget for displaying no bids state
class NoBidsWidget extends StatelessWidget {
  const NoBidsWidget({super.key, this.message, this.onPlaceFirstBid});
  final String? message;
  final VoidCallback? onPlaceFirstBid;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.gavel_outlined,
            size: 64,
            color: theme.textTheme.bodySmall?.color?.withAlpha(
              (0.3 * 255).toInt(),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            message ?? 'لا توجد عطاءات حتى الآن',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.textTheme.bodySmall?.color?.withAlpha(
                (0.7 * 255).toInt(),
              ),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'كن أول من يضع عطاءً على هذا المنتج',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.textTheme.bodySmall?.color?.withAlpha(
                (0.5 * 255).toInt(),
              ),
            ),
            textAlign: TextAlign.center,
          ),
          if (onPlaceFirstBid != null) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: onPlaceFirstBid,
              icon: const Icon(Icons.add),
              label: const Text('ضع أول عطاء'),
            ),
          ],
        ],
      ),
    );
  }
}
