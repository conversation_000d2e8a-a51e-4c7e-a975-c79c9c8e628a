/// شاشة تفاصيل المزاد
///
/// تعرض هذه الشاشة جميع المعلومات الحيوية لمنتج معروض في مزاد.
/// تشمل معرض صور للمنتج، مؤقت للعد التنازلي لنهاية المزاد، السعر الحالي،
/// سجل المزايدات السابقة، ووصف تفصيلي للمنتج.
/// كما تتيح للمستخدمين المؤهلين تقديم مزايدات جديدة.
library;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/widgets/app_error_widget.dart';
import '../../../core/widgets/loading_indicators.dart';
import '../providers/auction_provider.dart';
import '../widgets/auction_timer_widget.dart';
import '../widgets/bid_history_widget.dart';
import '../widgets/bid_stats_widget.dart';

class AuctionDetailScreen extends ConsumerStatefulWidget {
  const AuctionDetailScreen({required this.partId, super.key});
  final String partId;

  @override
  ConsumerState<AuctionDetailScreen> createState() =>
      _AuctionDetailScreenState();
}

class _AuctionDetailScreenState extends ConsumerState<AuctionDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;

  final PageController _imageController = PageController();
  final TextEditingController _bidController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  int _currentImageIndex = 0;

  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    _imageController.dispose();
    _bidController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final partAsync = ref.watch(auctionProvider(widget.partId));
    final bidsAsync = ref.watch(auctionBidsProvider(widget.partId));

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: partAsync.when(
        data: (part) {
          if (part == null) return const SizedBox.shrink();
          final isAuction = part.auctionStartDate != null;
          final hasEnded =
              isAuction &&
              part.auctionEndDate != null &&
              DateTime.now().isAfter(part.auctionEndDate as DateTime);

          return CustomScrollView(
            slivers: [
              _buildSliverAppBar(part),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    _buildImageCarousel(part),
                    _buildProductInfo(part),
                    if (isAuction) ...[
                      if (!hasEnded) _buildAuctionTimer(part),
                      _buildBidStats(),
                      _buildCurrentBidSection(bidsAsync),
                      _buildBidHistory(),
                    ],
                    _buildSellerInfo(part),
                    _buildProductDetails(part),
                    _buildCompatibilityInfo(part),
                    const SizedBox(height: 100), // Space for floating button
                  ],
                ),
              ),
            ],
          );
        },
        loading: () => const Scaffold(body: Center(child: LoadingSpinner())),
        error: (error, stackTrace) => Scaffold(
          appBar: AppBar(title: const Text('خطأ')),
          body: Center(
            child: AppErrorWidget(
              message: 'خطأ في تحميل تفاصيل المنتج',
              details: error.toString(),
              stackTrace: stackTrace,
              onRetry: () => ref.invalidate(auctionProvider(widget.partId)),
            ),
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildSliverAppBar(dynamic part) => SliverAppBar(
    expandedHeight: 300,
    pinned: true,
    backgroundColor: Theme.of(context).colorScheme.primary,
    flexibleSpace: FlexibleSpaceBar(
      title: Text(
        (part.name as String?) ?? 'منتج بدون اسم',
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(offset: Offset(0, 1), blurRadius: 3, color: Colors.black45),
          ],
        ),
      ),
      background: (part.images as List?)?.isNotEmpty == true
          ? CachedNetworkImage(
              imageUrl: ((part.images as List?)?.first as String?) ?? '',
              fit: BoxFit.cover,
              errorWidget: (context, url, error) => Container(
                color: Colors.grey.shade300,
                child: const Icon(Icons.image_not_supported, size: 50),
              ),
            )
          : Container(
              color: Colors.grey.shade300,
              child: const Icon(Icons.image_not_supported, size: 50),
            ),
    ),
    actions: [
      IconButton(
        icon: const Icon(Icons.share, color: Colors.white),
        onPressed: _shareProduct,
      ),
      IconButton(
        icon: Icon(
          _isFavorite ? Icons.favorite : Icons.favorite_border,
          color: Colors.white,
        ),
        onPressed: _toggleFavorite,
      ),
    ],
  );

  Widget _buildImageCarousel(dynamic part) {
    if ((part.images as List?)?.isEmpty != false) {
      return Container(
        height: 250,
        color: Colors.grey.shade200,
        child: const Center(
          child: Icon(Icons.image_not_supported, size: 80, color: Colors.grey),
        ),
      );
    }

    return SizedBox(
      height: 250,
      child: Stack(
        children: [
          PageView.builder(
            controller: _imageController,
            onPageChanged: (index) {
              setState(() {
                _currentImageIndex = index;
              });
            },
            itemCount: part.images.length as int,
            itemBuilder: (context, index) => GestureDetector(
              onTap: () => _showImageViewer(part.images as List<String>, index),
              child: CachedNetworkImage(
                imageUrl: part.images[index] as String,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade200,
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200,
                  child: const Icon(Icons.error, size: 50),
                ),
              ),
            ),
          ),
          if (part.images.length as int > 1) ...[
            Positioned(
              bottom: 16,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  part.images.length as int,
                  (index) => AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: index == _currentImageIndex ? 24 : 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: index == _currentImageIndex
                          ? Theme.of(context).colorScheme.primary
                          : Colors.white.withAlpha((0.5 * 255).round()),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha((0.6 * 255).round()),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_currentImageIndex + 1} / ${part.images.length as int}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductInfo(dynamic part) => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withAlpha((0.10 * 255).toInt()),
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    part.name?.toString() ?? 'منتج بدون اسم',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (part.brand != null) ...[
                    Text(
                      'الماركة: ${part.brand}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                  if (part.model != null) ...[
                    Text(
                      'الموديل: ${part.model}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            _buildConditionBadge(part.condition as String),
          ],
        ),
        const SizedBox(height: 16),
        if (part.description != null) ...[
          Text(
            'الوصف',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            part.description! as String,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(height: 1.5),
          ),
          const SizedBox(height: 16),
        ],
        _buildPriceSection(part),
      ],
    ),
  );

  Widget _buildConditionBadge(String? condition) {
    Color badgeColor;
    String badgeText;

    switch (condition?.toLowerCase()) {
      case 'new':
        badgeColor = Theme.of(context).colorScheme.primary;
        badgeText = 'جديد';
        break;
      case 'like_new':
        badgeColor = Colors.blue;
        badgeText = 'كالجديد';
        break;
      case 'used':
        badgeColor = Colors.orange;
        badgeText = 'مستعمل';
        break;
      case 'fair':
        badgeColor = Colors.amber;
        badgeText = 'حالة جيدة';
        break;
      case 'poor':
        badgeColor = Colors.red;
        badgeText = 'حالة ضعيفة';
        break;
      default:
        badgeColor = Colors.grey;
        badgeText = 'غير محدد';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor.withAlpha((0.1 * 255).round()),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: badgeColor.withAlpha((0.3 * 255).round())),
      ),
      child: Text(
        badgeText,
        style: TextStyle(
          color: badgeColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildPriceSection(dynamic part) {
    final isAuction = part.listingType == 'auction';

    if (isAuction) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'السعر الأساسي',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 4),
          Text(
            '${part.price?.toStringAsFixed(0) ?? '0'} د.ل',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Text(
            'السعر: ',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          ),
          Text(
            '${part.price?.toStringAsFixed(0) ?? '0'} د.ل',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );
    }
  }

  Widget _buildAuctionTimer(dynamic part) {
    if (part.auctionEndDate == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: AuctionTimerWidget(
        auctionEndDate: part.auctionEndDate as DateTime,
        onAuctionEnd: () {
          setState(() {
            // Refresh the part data when auction ends
            ref.invalidate(auctionProvider(widget.partId));
          });
        },
      ),
    );
  }

  Widget _buildBidStats() => Container(
    margin: const EdgeInsets.all(16),
    child: BidStatsWidget(partId: widget.partId),
  );

  Widget _buildCurrentBidSection(AsyncValue<List<dynamic>> bidsAsync) =>
      Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha((0.10 * 255).toInt()),
              spreadRadius: 1,
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: bidsAsync.when(
          data: (bids) {
            if (bids.isEmpty) {
              return Column(
                children: [
                  Text(
                    'لا توجد عطاءات حتى الآن',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'كن أول من يضع عطاءاً!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              );
            }

            final highestBid = bids.first;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أعلى عطاء حالي',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      '${highestBid.amount.toStringAsFixed(0)} د.ل',
                      style: Theme.of(context).textTheme.headlineLarge
                          ?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Spacer(),
                    Text(
                      DateFormat('HH:mm - dd/MM/yyyy').format(
                        (highestBid.createdAt as DateTime?) ?? DateTime.now(),
                      ),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text(
            'خطأ في تحميل العطاءات',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.red),
          ),
        ),
      );

  Widget _buildBidHistory() => Container(
    margin: const EdgeInsets.all(16),
    child: BidHistoryWidget(partId: widget.partId),
  );

  Widget _buildSellerInfo(dynamic part) => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withAlpha((0.10 * 255).toInt()),
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات البائع',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundImage: part.sellerImageUrl != null
                  ? NetworkImage(part.sellerImageUrl! as String)
                  : null,
              child: part.sellerImageUrl == null
                  ? Text((part.sellerName as String?)?.substring(0, 1) ?? '؟')
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    part.sellerName?.toString() ?? 'بائع غير محدد',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (part.sellerLocation != null)
                    Text(
                      part.sellerLocation! as String,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                ],
              ),
            ),
            IconButton(
              onPressed: _contactSeller,
              icon: const Icon(Icons.message),
              color: Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
      ],
    ),
  );

  Widget _buildProductDetails(dynamic part) => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withAlpha((0.10 * 255).toInt()),
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تفاصيل القطعة',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildDetailRow('رقم القطعة', part.partNumber as String),
        _buildDetailRow('الشركة المصنعة', part.manufacturer as String),
        _buildDetailRow('بلد الصنع', part.countryOfOrigin as String),
        _buildDetailRow('الضمان', part.warranty as String),
        _buildDetailRow(
          'تاريخ النشر',
          DateFormat(
            'dd/MM/yyyy',
          ).format((part.createdAt as DateTime?) ?? DateTime.now()),
        ),
      ],
    ),
  );

  Widget _buildDetailRow(String label, String? value) {
    if (value == null || value.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Widget _buildCompatibilityInfo(dynamic part) => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withAlpha((0.10 * 255).toInt()),
          spreadRadius: 1,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التوافق مع المركبات',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        if (part.compatibleVehicles != null &&
            (part.compatibleVehicles as List).isNotEmpty)
          ...(part.compatibleVehicles as List<String>).map(
            (vehicle) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.directions_car,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(vehicle, style: Theme.of(context).textTheme.bodyMedium),
                ],
              ),
            ),
          )
        else
          Text(
            'لم يتم تحديد المركبات المتوافقة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
      ],
    ),
  );

  // Helper methods
  void _shareProduct() {
    SharePlus.instance.share(
      ShareParams(text: 'تحقق من هذه القطعة في كارناو!'),
    );
  }

  void _toggleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isFavorite
              ? 'تم إضافة المنتج للمفضلة'
              : 'تم إزالة المنتج من المفضلة',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _contactSeller() {
    final authState = ref.read(simpleSupabaseAuthProvider);

    // Handle user authentication state
    if (authState is SimpleAuthStateAuthenticated) {
      // Navigate to chat or show contact options
      showModalBottomSheet<Widget>(
        context: context,
        builder: (context) => _buildContactSellerSheet(),
      );
    } else {
      _showLoginRequired();
    }
  }

  Widget _buildContactSellerSheet() => Container(
    padding: const EdgeInsets.all(24),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'التواصل مع البائع',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 24),
        ListTile(
          leading: Icon(
            Icons.message,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: const Text('إرسال رسالة'),
          subtitle: const Text('تواصل مع البائع عبر الرسائل'),
          onTap: () {
            Navigator.pop(context);
            // Navigate to chat screen
            context.push('/chat/${widget.partId}');
          },
        ),
        ListTile(
          leading: Icon(
            Icons.phone,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: const Text('اتصال هاتفي'),
          subtitle: const Text('الاتصال المباشر بالبائع'),
          onTap: () {
            Navigator.pop(context);
            _makePhoneCall();
          },
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: FilledButton(
            onPressed: () => Navigator.pop(context),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.grey.shade300,
              foregroundColor: Colors.black87,
            ),
            child: const Text('إلغاء'),
          ),
        ),
      ],
    ),
  );

  void _makePhoneCall() {
    // Implement phone call functionality
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ستتمكن من الاتصال قريباً')));
  }

  void _showLoginRequired() {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الدخول مطلوب'),
        content: const Text('يجب تسجيل الدخول للتفاعل مع المنتجات'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              context.push('/login');
            },
            child: const Text('تسجيل الدخول'),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    final partAsync = ref.watch(auctionProvider(widget.partId));
    final authState = ref.watch(simpleSupabaseAuthProvider);

    return partAsync.maybeWhen(
      data: (part) {
        if (part == null) return const SizedBox.shrink();
        final isAuction = part.auctionStartDate != null;
        final hasEnded =
            isAuction &&
            part.auctionEndDate != null &&
            DateTime.now().isAfter(part.auctionEndDate as DateTime);

        final isLoggedIn = authState is SimpleAuthStateAuthenticated;

        if (isAuction && !hasEnded) {
          // Non-constant FloatingActionButton for auction
          return FloatingActionButton.extended(
            onPressed: isLoggedIn
                ? () => _showBidDialog(part)
                : _showLoginRequired,
            backgroundColor: Theme.of(context).colorScheme.primary,
            icon: const Icon(Icons.gavel, color: Colors.white),
            label: Text(
              'ضع عطاءك',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
        }

        // Non-constant FloatingActionButton for regular product
        return FloatingActionButton.extended(
          onPressed: isLoggedIn ? _contactSeller : _showLoginRequired,
          backgroundColor: Theme.of(context).colorScheme.primary,
          icon: const Icon(Icons.shopping_cart, color: Colors.white),
          label: Text(
            'اتصل بالبائع',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
      },
      orElse: () => const SizedBox.shrink(),
    );
  }

  Future<void> _showBidDialog(dynamic part) async {
    await showDialog<Widget>(
      context: context,
      builder: (BuildContext context) => _buildBidDialog(part),
    );
  }

  Widget _buildBidDialog(dynamic part) {
    final bidsAsync = ref.watch(auctionBidsProvider(widget.partId));

    return AlertDialog(
      title: const Text('ضع عطاءك'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            bidsAsync.when(
              data: (bids) {
                final minimumBid = bids.isNotEmpty && bids.first.amount != null
                    ? (bids.first.amount! + 10).toInt()
                    : (part.price?.toInt() ?? 0);

                return Text(
                  'الحد الأدنى للعطاء: ${minimumBid.toString()} د.ل',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
                );
              },
              loading: () => const SizedBox.shrink(),
              error: (error, stack) => const SizedBox.shrink(),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _bidController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'مبلغ العطاء (د.ل)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.attach_money),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال مبلغ العطاء';
                }

                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'يرجى إدخال مبلغ صالح';
                }

                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            _bidController.clear();
          },
          child: const Text('إلغاء'),
        ),
        FilledButton(
          onPressed: () => _submitBid(part),
          child: const Text('تأكيد العطاء'),
        ),
      ],
    );
  }

  Future<void> _submitBid(dynamic part) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_bidController.text);

    try {
      final auctionService = ref.read(auctionServiceProvider);
      await auctionService.placeBid(widget.partId, amount);

      if (mounted) {
        Navigator.pop(context);
        _bidController.clear();

        // Refresh the bids
        ref.invalidate(auctionBidsProvider(widget.partId));

        _showSuccessBidSnackbar();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في وضع العطاء: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showSuccessBidSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم وضع العطاء بنجاح!'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Future<void> _showImageViewer(List<String> images, int initialIndex) async =>
      showDialog<Widget>(
        context: context,
        builder: (BuildContext context) => Dialog(
          backgroundColor: Colors.black,
          insetPadding: EdgeInsets.zero,
          child: Stack(
            children: [
              PageView.builder(
                controller: PageController(initialPage: initialIndex),
                itemCount: images.length,
                itemBuilder: (BuildContext context, int index) =>
                    InteractiveViewer(
                      child: CachedNetworkImage(
                        imageUrl: images[index],
                        fit: BoxFit.contain,
                        errorWidget: (context, url, error) => const Center(
                          child: Icon(
                            Icons.error,
                            color: Colors.white,
                            size: 50,
                          ),
                        ),
                      ),
                    ),
              ),
              Positioned(
                top: 40,
                right: 16,
                child: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white, size: 30),
                ),
              ),
            ],
          ),
        ),
      );
}
