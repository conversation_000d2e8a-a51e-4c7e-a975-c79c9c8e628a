import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';

// Sales Report Models
class SalesReportModel {
  const SalesReportModel({
    required this.period,
    required this.totalSales,
    required this.totalOrders,
    required this.averageOrderValue,
    required this.totalCustomers,
    required this.growthRate,
    required this.dailySales,
    required this.topProducts,
    required this.categoryBreakdown,
    required this.customerSegments,
  });

  final String period;
  final double totalSales;
  final int totalOrders;
  final double averageOrderValue;
  final int totalCustomers;
  final double growthRate;
  final List<SalesDataPoint> dailySales;
  final List<ProductSalesData> topProducts;
  final List<CategorySalesData> categoryBreakdown;
  final List<CustomerSegmentData> customerSegments;
}

class SalesDataPoint {
  const SalesDataPoint({
    required this.date,
    required this.sales,
    required this.orders,
  });

  final DateTime date;
  final double sales;
  final int orders;
}

class ProductSalesData {
  const ProductSalesData({
    required this.name,
    required this.sales,
    required this.quantity,
    required this.percentage,
  });

  final String name;
  final double sales;
  final int quantity;
  final double percentage;
}

class CategorySalesData {
  const CategorySalesData({
    required this.name,
    required this.sales,
    required this.percentage,
  });

  final String name;
  final double sales;
  final double percentage;
}

class CustomerSegmentData {
  const CustomerSegmentData({
    required this.segment,
    required this.count,
    required this.sales,
    required this.percentage,
  });

  final String segment;
  final int count;
  final double sales;
  final double percentage;
}

// Sales Reports Provider
final salesReportsProvider = FutureProvider.family<SalesReportModel, String>((
  ref,
  period,
) async {
  final apiClient = ref.read(simpleApiClientProvider);
  final isAuthenticated = ref.read(isAuthenticatedProvider);
  final currentUser = ref.read(currentUserProvider);

  if (!isAuthenticated || currentUser == null) {
    throw Exception('المستخدم غير مسجل الدخول');
  }

  try {
    // حساب تواريخ الفترة
    final endDate = DateTime.now();
    late DateTime startDate;

    switch (period) {
      case '7d':
        startDate = endDate.subtract(const Duration(days: 7));
        break;
      case '30d':
        startDate = endDate.subtract(const Duration(days: 30));
        break;
      case '90d':
        startDate = endDate.subtract(const Duration(days: 90));
        break;
      case '1y':
        startDate = endDate.subtract(const Duration(days: 365));
        break;
      default:
        startDate = endDate.subtract(const Duration(days: 30));
    }

    // جلب إجمالي المبيعات
    final salesResponse = await apiClient.get(
      '/orders/total-sales',
      queryParameters: {
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'seller_id': currentUser.id,
      },
    );

    final totalSales = (salesResponse.data['total_amount'] as num).toDouble();
    final totalOrders = salesResponse.data['total_orders'] as int;
    final averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0.0;

    // جلب عدد العملاء الفريدين
    final customersResponse = await apiClient.get(
      '/orders/unique-customers',
      queryParameters: {
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'seller_id': currentUser.id,
      },
    );

    final uniqueCustomers = (customersResponse.data['count'] as int);

    // جلب البيانات اليومية
    final dailySalesData = await _getDailySalesData(
      apiClient,
      currentUser.id,
      startDate,
      endDate,
    );

    // جلب أفضل المنتجات
    final topProducts = await _getTopProducts(
      apiClient,
      currentUser.id,
      startDate,
      endDate,
    );

    // جلب تفصيل الفئات
    final categoryBreakdown = await _getCategoryBreakdown(
      apiClient,
      currentUser.id,
      startDate,
      endDate,
    );

    // جلب قطاعات العملاء
    final customerSegments = await _getCustomerSegments(
      apiClient,
      currentUser.id,
      startDate,
      endDate,
    );

    // حساب معدل النمو (مقارنة بالفترة السابقة)
    final previousStartDate = startDate.subtract(endDate.difference(startDate));
    final previousSalesResponse = await apiClient.get(
      '/orders/total-sales',
      queryParameters: {
        'start_date': previousStartDate.toIso8601String(),
        'end_date': startDate.toIso8601String(),
        'seller_id': currentUser.id,
      },
    );

    final previousTotalSales =
        (previousSalesResponse.data['total_amount'] as num).toDouble();

    final growthRate = previousTotalSales > 0
        ? ((totalSales - previousTotalSales) / previousTotalSales) * 100
        : 0.0;

    return SalesReportModel(
      period: _getPeriodLabel(period),
      totalSales: totalSales,
      totalOrders: totalOrders,
      averageOrderValue: averageOrderValue,
      totalCustomers: uniqueCustomers,
      growthRate: growthRate,
      dailySales: dailySalesData,
      topProducts: topProducts,
      categoryBreakdown: categoryBreakdown,
      customerSegments: customerSegments,
    );
  } catch (e) {
    throw Exception('خطأ في تحميل تقرير المبيعات: $e');
  }
});

// Helper function to get daily sales data
Future<List<SalesDataPoint>> _getDailySalesData(
  dynamic apiClient,
  String? userId,
  DateTime startDate,
  DateTime endDate,
) async {
  final response = await apiClient.get(
    '/orders/daily-sales',
    queryParameters: {
      'seller_id': userId,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
    },
  );

  final dailyData = <String, Map<String, dynamic>>{};

  for (final order in response as List<dynamic>) {
    final orderDate = DateTime.parse(order['created_at'].toString()).toLocal();
    final dateKey =
        '${orderDate.year}-${orderDate.month.toString().padLeft(2, '0')}-${orderDate.day.toString().padLeft(2, '0')}';

    if (!dailyData.containsKey(dateKey)) {
      dailyData[dateKey] = {'sales': 0.0, 'orders': 0, 'date': orderDate};
    }

    dailyData[dateKey]!['sales'] =
        (dailyData[dateKey]!['sales'] as double) +
        (order['total_amount'] as num).toDouble();
    dailyData[dateKey]!['orders'] = (dailyData[dateKey]!['orders'] as int) + 1;
  }

  return dailyData.values
      .map(
        (data) => SalesDataPoint(
          date: data['date'] as DateTime,
          sales: data['sales'] as double,
          orders: data['orders'] as int,
        ),
      )
      .toList();
}

// Helper function to get top products
Future<List<ProductSalesData>> _getTopProducts(
  dynamic apiClient,
  String? userId,
  DateTime startDate,
  DateTime endDate,
) async {
  final response = await apiClient.get(
    '/order-items/top-products',
    queryParameters: {
      'seller_id': userId,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
    },
  );

  final productData = <String, Map<String, dynamic>>{};

  for (final item in response as List<dynamic>) {
    final productName = item['product_name'] as String;
    final quantity = item['quantity'] as int;
    final unitPrice = (item['unit_price'] as num).toDouble();
    final sales = (quantity * unitPrice).toDouble();

    if (!productData.containsKey(productName)) {
      productData[productName] = {'sales': 0.0, 'quantity': 0};
    }

    productData[productName]!['sales'] =
        (productData[productName]!['sales'] as double) + sales;
    productData[productName]!['quantity'] =
        (productData[productName]!['quantity'] as int) + quantity;
  }

  final totalSales = productData.values.fold<double>(
    0,
    (sum, data) => sum + (data['sales'] as double),
  );

  final sortedProducts = productData.entries.toList()
    ..sort(
      (a, b) =>
          (b.value['sales'] as double).compareTo(a.value['sales'] as double),
    );

  return sortedProducts.take(5).map((entry) {
    final sales = entry.value['sales'] as double;
    final percentage = totalSales > 0 ? (sales / totalSales) * 100.0 : 0.0;

    return ProductSalesData(
      name: entry.key,
      sales: sales,
      quantity: entry.value['quantity'] as int,
      percentage: percentage,
    );
  }).toList();
}

// Helper function to get category breakdown
Future<List<CategorySalesData>> _getCategoryBreakdown(
  dynamic apiClient,
  String? userId,
  DateTime startDate,
  DateTime endDate,
) async {
  // This would require joining with products and categories tables
  // For now, return empty list - implement based on your database schema
  return [];
}

// Helper function to get customer segments
Future<List<CustomerSegmentData>> _getCustomerSegments(
  dynamic apiClient,
  String? userId,
  DateTime startDate,
  DateTime endDate,
) async {
  // This would require analyzing customer purchase patterns
  // For now, return empty list - implement based on your business logic
  return [];
}

String _getPeriodLabel(String period) {
  switch (period) {
    case '7d':
      return 'آخر 7 أيام';
    case '30d':
      return 'آخر 30 يوم';
    case '90d':
      return 'آخر 3 أشهر';
    case '1y':
      return 'آخر سنة';
    default:
      return 'آخر 30 يوم';
  }
}

/// شاشة تقارير المبيعات
///
/// تعرض تحليلات مفصَّلة لأداء المبيعات مثل إجمالي المبيعات، عدد الطلبات،
/// المنتجات الأكثر مبيعاً، والفئات الأعلى أداءً. تشمل أيضاً مخططات زمنية
/// ومؤشرات نمو قابلة للتصفية بحسب الفترة الزمنية.
class SalesReportsScreen extends HookConsumerWidget {
  const SalesReportsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPeriod = useState<String>('30d');

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'تقارير المبيعات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.date_range),
            onSelected: (value) => selectedPeriod.value = value,
            itemBuilder: (context) => [
              const PopupMenuItem(value: '7d', child: Text('آخر 7 أيام')),
              const PopupMenuItem(value: '30d', child: Text('آخر 30 يوم')),
              const PopupMenuItem(value: '90d', child: Text('آخر 3 أشهر')),
              const PopupMenuItem(value: '1y', child: Text('آخر سنة')),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {
              // Export report functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('سيتم تصدير التقرير قريباً')),
              );
            },
          ),
        ],
      ),
      body: ref
          .watch(salesReportsProvider(selectedPeriod.value))
          .when(
            data: (salesReport) => RefreshIndicator(
              onRefresh: () async {
                ref.invalidate(salesReportsProvider(selectedPeriod.value));
              },
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSummarySection(context, salesReport),
                    const SizedBox(height: 24),
                    _buildSalesChart(context, salesReport.dailySales),
                    const SizedBox(height: 24),
                    _buildTopProducts(context, salesReport.topProducts),
                    const SizedBox(height: 24),
                    _buildCategoryBreakdown(
                      context,
                      salesReport.categoryBreakdown,
                    ),
                    const SizedBox(height: 24),
                    _buildCustomerSegments(
                      context,
                      salesReport.customerSegments,
                    ),
                  ],
                ),
              ),
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: SelectableText.rich(
                TextSpan(
                  children: [
                    const TextSpan(
                      text: 'خطأ في تحميل تقرير المبيعات: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    TextSpan(
                      text: error.toString(),
                      style: const TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  Widget _buildSummarySection(BuildContext context, SalesReportModel report) =>
      Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'ملخص المبيعات',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.trending_up,
                          size: 16,
                          color: Colors.green.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '+${report.growthRate.toStringAsFixed(1)}%',
                          style: TextStyle(
                            color: Colors.green.shade600,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 2,
                children: [
                  _buildSummaryCard(
                    'إجمالي المبيعات',
                    '${report.totalSales.toStringAsFixed(2)} د.ل',
                    Icons.attach_money,
                    Colors.green,
                  ),
                  _buildSummaryCard(
                    'إجمالي الطلبات',
                    '${report.totalOrders}',
                    Icons.shopping_cart,
                    Colors.blue,
                  ),
                  _buildSummaryCard(
                    'متوسط قيمة الطلب',
                    '${report.averageOrderValue.toStringAsFixed(2)} د.ل',
                    Icons.trending_up,
                    Colors.orange,
                  ),
                  _buildSummaryCard(
                    'إجمالي العملاء',
                    '${report.totalCustomers}',
                    Icons.people,
                    Colors.purple,
                  ),
                ],
              ),
            ],
          ),
        ),
      );

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) => Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: color.withAlpha(25),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: color.withAlpha(51)),
    ),
    child: Row(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                title,
                style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
              ),
            ],
          ),
        ),
      ],
    ),
  );

  Widget _buildSalesChart(
    BuildContext context,
    List<SalesDataPoint> salesData,
  ) => Card(
    elevation: 2,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مخطط المبيعات اليومية',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                'مخطط المبيعات اليومية\n(يتطلب مكتبة الرسوم البيانية)',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey, fontSize: 14),
              ),
            ),
          ),
        ],
      ),
    ),
  );

  Widget _buildTopProducts(
    BuildContext context,
    List<ProductSalesData> products,
  ) => Card(
    elevation: 2,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أفضل المنتجات مبيعاً',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...products.map(
            (product) => _buildProductSalesItem(context, product),
          ),
        ],
      ),
    ),
  );

  Widget _buildProductSalesItem(
    BuildContext context,
    ProductSalesData product,
  ) => Padding(
    padding: const EdgeInsets.only(bottom: 12),
    child: Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                product.name,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    '${product.sales.toStringAsFixed(0)} د.ل',
                    style: TextStyle(
                      color: Colors.green.shade600,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '${product.quantity} قطعة',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
        Text(
          '${product.percentage.toStringAsFixed(1)}%',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ],
    ),
  );

  Widget _buildCategoryBreakdown(
    BuildContext context,
    List<CategorySalesData> categories,
  ) => Card(
    elevation: 2,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تحليل المبيعات حسب الفئة',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...categories.map(
            (category) => _buildCategorySalesItem(context, category),
          ),
        ],
      ),
    ),
  );

  Widget _buildCategorySalesItem(
    BuildContext context,
    CategorySalesData category,
  ) => Padding(
    padding: const EdgeInsets.only(bottom: 16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              category.name,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
            ),
            Text(
              '${category.percentage.toStringAsFixed(1)}%',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: category.percentage / 100,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${category.sales.toStringAsFixed(0)} د.ل',
          style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
        ),
      ],
    ),
  );

  Widget _buildCustomerSegments(
    BuildContext context,
    List<CustomerSegmentData> segments,
  ) => Card(
    elevation: 2,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تحليل شرائح العملاء',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...segments.map(
            (segment) => _buildCustomerSegmentItem(context, segment),
          ),
        ],
      ),
    ),
  );

  Widget _buildCustomerSegmentItem(
    BuildContext context,
    CustomerSegmentData segment,
  ) => Container(
    margin: const EdgeInsets.only(bottom: 12),
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.grey.shade200),
    ),
    child: Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                segment.segment,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${segment.count} عميل - '
                '${segment.sales.toStringAsFixed(0)} د.ل',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              ),
            ],
          ),
        ),
        const Spacer(),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${segment.sales.toStringAsFixed(0)} د.ل',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              '${segment.percentage.toStringAsFixed(1)}% من المبيعات',
              style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
            ),
          ],
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 40,
          height: 40,
          child: CircularProgressIndicator(
            value: segment.percentage / 100,
            strokeWidth: 5,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ],
    ),
  );
}
