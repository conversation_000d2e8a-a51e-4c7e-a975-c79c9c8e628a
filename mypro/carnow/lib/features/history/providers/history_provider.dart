import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:carnow/features/products/models/product_model.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../models/history_model.dart';

part 'history_provider.g.dart';

final _logger = Logger('HistoryProvider');
typedef Json = Map<String, dynamic>;

/// History provider - Clean implementation using Go backend
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
class HistoryNotifier extends _$HistoryNotifier {
  @override
  Future<List<HistoryModel>> build() => _fetchHistory();

  Future<List<HistoryModel>> _fetchHistory() async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      final currentUser = ref.watch(currentUserProvider);
      
      if (currentUser?.id == null) {
        return [];
      }

      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/user/history',
      );

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch history: ${response.message}');
        return [];
      }

      final data = response.data!;
      final historyItems = <HistoryModel>[];
      
      final historyJson = data.containsKey('data') && data['data'] is List
          ? data['data'] as List<dynamic>
          : data.containsKey('history') && data['history'] is List
              ? data['history'] as List<dynamic>
              : <dynamic>[];
      
      for (final item in historyJson) {
        try {
          final json = item as Json;
          final productJson = json['product'] as Json?;
          final product = productJson != null
              ? ProductModel.fromJson(productJson)
              : null;

          if (product != null) {
            final historyItem = HistoryModel.fromProduct(
              product,
              viewedAt: DateTime.parse(json['viewed_at'] as String),
            );
            historyItems.add(historyItem);
          }
        } catch (e, _) {
          _logger.severe('Error parsing history item: $e');
        }
      }

      return historyItems;
    } catch (e) {
      _logger.severe('Error fetching history: $e');
      return [];
    }
  }

  Future<void> addToHistory(String productId) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      final currentUser = ref.watch(currentUserProvider);

      if (currentUser?.id == null) return;

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/user/history',
        data: {
          'product_id': productId,
          'viewed_at': DateTime.now().toIso8601String(),
        },
      );

      if (!response.isSuccess) {
        _logger.warning('Failed to add to history: ${response.message}');
      }

      ref.invalidateSelf();
    } catch (e, _) {
      _logger.severe('Error adding to history: $e');
      rethrow;
    }
  }

  Future<void> clearHistory() async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      final currentUser = ref.watch(currentUserProvider);

      if (currentUser?.id == null) return;

      final response = await apiClient.deleteApi('/user/history');

      if (!response.isSuccess) {
        _logger.warning('Failed to clear history: ${response.message}');
      }

      ref.invalidateSelf();
    } catch (e) {
      _logger.severe('Error clearing history: $e');
      rethrow;
    }
  }
}
