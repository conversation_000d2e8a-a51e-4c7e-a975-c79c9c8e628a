import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/favorites_provider.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';


class FavoriteButton extends ConsumerWidget {
  const FavoriteButton({
    super.key,
    required this.productId,
    this.size = 24.0,
    this.color,
  });

  final String productId;
  final double size;
  final Color? color;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(simpleSupabaseAuthProvider);
    
    // Safe check for authenticated user
    if (authState is! SimpleAuthStateAuthenticated) {
      return IconButton(
        onPressed: () {
          // Redirect to login
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يرجى تسجيل الدخول أولاً لإضافة المنتجات للمفضلة'),
            ),
          );
        },
        icon: Icon(
          Icons.favorite_border,
          size: size,
          color: color ?? Theme.of(context).iconTheme.color,
        ),
      );
    }

    final isFavorite = ref.watch(isFavoriteProvider(productId));

    return IconButton(
      onPressed: () async {
        try {
          await ref
              .read(favoritesNotifierProvider.notifier)
              .toggleFavorite(productId);
        } catch (e) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في تحديث المفضلة: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      icon: Icon(
        isFavorite ? Icons.favorite : Icons.favorite_border,
        size: size,
        color: isFavorite 
            ? Colors.red 
            : (color ?? Theme.of(context).iconTheme.color),
      ),
      tooltip: isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة',
    );
  }
}
