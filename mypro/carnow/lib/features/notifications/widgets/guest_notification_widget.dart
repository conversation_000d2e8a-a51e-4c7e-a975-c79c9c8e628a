import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/theme/app_theme.dart';
import '../../../l10n/app_localizations.dart';
import '../services/notification_service.dart';

/// Widget الإشعارات للمستخدمين غير المسجلين
/// يعرض رسالة تطلب من المستخدم تسجيل الدخول أو إنشاء حساب
class GuestNotificationWidget extends ConsumerWidget {
  const GuestNotificationWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withAlpha(25),
            theme.colorScheme.secondary.withAlpha(25),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: theme.colorScheme.primary.withAlpha(77)),
      ),
      child: Column(
        children: [
          // رمز الإشعارات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(38),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.notifications_outlined,
              size: 48,
              color: theme.colorScheme.primary,
            ),
          ),

          const SizedBox(height: AppTheme.spacingM),

          // العنوان
          Text(
            'مرحباً بك في CarNow!',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppTheme.spacingS),

          // الرسالة الرئيسية
          Text(
            'للحصول على آخر الإشعارات حول المنتجات والعروض والطلبات، يرجى تسجيل الدخول أو إنشاء حساب جديد',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(204),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppTheme.spacingM),

          // قائمة المزايا
          _buildFeaturesList(context, theme),

          const SizedBox(height: AppTheme.spacingL),

          // أزرار التسجيل
          _buildActionButtons(context, theme, l10n),
        ],
      ),
    );
  }

  /// بناء قائمة المزايا
  Widget _buildFeaturesList(BuildContext context, ThemeData theme) {
    final features = [
      {
        'icon': Icons.notifications_active,
        'text': 'إشعارات فورية عن المنتجات الجديدة',
      },
      {
        'icon': Icons.local_offer,
        'text': 'تنبيهات حول العروض والخصومات الخاصة',
      },
      {'icon': Icons.shopping_cart, 'text': 'متابعة حالة الطلبات والمشتريات'},
      {'icon': Icons.favorite, 'text': 'تنبيهات حول المنتجات المفضلة'},
      {'icon': Icons.message, 'text': 'إشعارات الرسائل والمحادثات'},
    ];

    return Column(
      children: features
          .map(
            (feature) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Icon(
                    feature['icon'] as IconData,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      feature['text'] as String,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(179),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
          .toList(),
    );
  }

  /// بناء أزرار العمل
  Widget _buildActionButtons(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Column(
      children: [
        // زر تسجيل الدخول
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _handleSignIn(context),
            icon: const Icon(Icons.login),
            label: const Text('تسجيل الدخول'),
            style: FilledButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingM),

        // زر إنشاء حساب جديد
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _handleSignUp(context),
            icon: const Icon(Icons.person_add),
            label: const Text('إنشاء حساب جديد'),
            style: OutlinedButton.styleFrom(
              foregroundColor: theme.colorScheme.primary,
              side: BorderSide(color: theme.colorScheme.primary),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingS),

        // رابط التصفح كزائر
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'المتابعة كزائر',
            style: TextStyle(
              color: theme.colorScheme.onSurface.withAlpha(153),
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  /// معالجة تسجيل الدخول
  void _handleSignIn(BuildContext context) {
    context.push('/login');
  }

  /// معالجة إنشاء حساب جديد
  void _handleSignUp(BuildContext context) {
    context.push('/register');
  }
}

/// Badge للإشعارات مع معالجة المستخدمين غير المسجلين
class UniversalNotificationBadge extends ConsumerWidget {
  const UniversalNotificationBadge({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    return IconButton(
      icon: Stack(
        clipBehavior: Clip.none,
        children: [
          const Icon(Icons.notifications),
          if (!isAuthenticated)
            Positioned(
              top: -5,
              right: -5,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(10),
                ),
                constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                child: const Text(
                  '!',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          // عرض عدد الإشعارات للمستخدمين المسجلين
          if (isAuthenticated)
            Consumer(
              builder: (context, ref, child) {
                // ربط العدد الفعلي للإشعارات غير المقروءة
                final unreadCount = ref
                    .watch(unreadNotificationCountProvider)
                    .when(
                      data: (count) => count,
                      loading: () => 0,
                      error: (error, st) => 0,
                    );

                if (unreadCount == 0) {
                  return const SizedBox.shrink();
                }

                return Positioned(
                  top: -5,
                  right: -5,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      unreadCount > 9 ? '9+' : unreadCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              },
            ),
        ],
      ),
      onPressed: () => _handleNotificationTap(context, isAuthenticated),
      tooltip: 'الإشعارات',
    );
  }

  /// معالجة الضغط على أيقونة الإشعارات
  void _handleNotificationTap(BuildContext context, bool isAuthenticated) {
    if (isAuthenticated) {
      // المستخدم مسجل - انتقال لشاشة الإشعارات
      context.push('/notifications');
    } else {
      // المستخدم غير مسجل - عرض dialog للتسجيل
      _showGuestNotificationDialog(context);
    }
  }

  /// عرض حوار الإشعارات للزوار
  void _showGuestNotificationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: const SingleChildScrollView(child: GuestNotificationWidget()),
      ),
    );
  }
}
