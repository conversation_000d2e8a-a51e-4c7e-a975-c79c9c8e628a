import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';

part 'auth_stream_provider.g.dart';

/// Stream provider for authentication state changes
@riverpod
Stream<SimpleAuthState> authStream(Ref ref) {
  // Watch the simple auth provider directly
  return Stream.periodic(const Duration(milliseconds: 500), (_) {
    final authState = ref.read(simpleSupabaseAuthProvider);
    return authState;
  }).distinct();
}
