// ============================================================================
// CarNow Unified Authentication System - Session Management Service
// ============================================================================
// File: session_management_service.dart
// Description: Comprehensive session management with secure logout functionality
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// Updated: 2024-01-25 - Simplified to use Simple Supabase Auth
// ============================================================================

import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/error/app_error.dart';
import '../../../core/error/app_error_factory.dart';
import '../../../core/error/retry_service.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/auth_models.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';

part 'session_management_service.g.dart';

/// Session state for tracking user sessions
enum SessionState {
  active,
  inactive,
  expired,
  terminated,
  suspended,
}

/// Session information
class SessionInfo {
  final String sessionId;
  final String deviceId;
  final String deviceName;
  final String deviceType;
  final String? ipAddress;
  final DateTime createdAt;
  final DateTime lastAccessedAt;
  final DateTime expiresAt;
  final SessionState state;
  final bool isCurrent;

  const SessionInfo({
    required this.sessionId,
    required this.deviceId,
    required this.deviceName,
    required this.deviceType,
    this.ipAddress,
    required this.createdAt,
    required this.lastAccessedAt,
    required this.expiresAt,
    required this.state,
    required this.isCurrent,
  });

  factory SessionInfo.fromJson(Map<String, dynamic> json) {
    return SessionInfo(
      sessionId: json['session_id'] ?? '',
      deviceId: json['device_id'] ?? '',
      deviceName: json['device_name'] ?? '',
      deviceType: json['device_type'] ?? 'unknown',
      ipAddress: json['ip_address'],
      createdAt: DateTime.parse(json['created_at']),
      lastAccessedAt: DateTime.parse(json['last_accessed_at']),
      expiresAt: DateTime.parse(json['expires_at']),
      state: SessionState.values.firstWhere(
        (s) => s.name == json['state'],
        orElse: () => SessionState.inactive,
      ),
      isCurrent: json['is_current'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'session_id': sessionId,
      'device_id': deviceId,
      'device_name': deviceName,
      'device_type': deviceType,
      'ip_address': ipAddress,
      'created_at': createdAt.toIso8601String(),
      'last_accessed_at': lastAccessedAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'state': state.name,
      'is_current': isCurrent,
    };
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isActive => state == SessionState.active && !isExpired;
}

/// Logout type for different logout scenarios
enum LogoutType {
  manual,        // User manually logged out
  automatic,     // Auto logout due to inactivity
  tokenExpired,  // Token expired
  security,      // Security-related logout
  deviceChange,  // Device change detected
  allDevices,    // Logout from all devices
}

/// Session management service with comprehensive logout functionality
class SessionManagementService {
  final SimpleApiClient _apiClient;
  final SimpleSupabaseAuthProvider _authProvider;
  final RetryService _retryService;
  late SharedPreferences _prefs;

  // Session monitoring
  Timer? _sessionTimer;
  Timer? _heartbeatTimer;
  final StreamController<SessionState> _sessionStateController = 
      StreamController<SessionState>.broadcast();
  
  SessionState _currentState = SessionState.inactive;
  String? _currentSessionId;
  DateTime? _lastActivity;

  // Configuration - Extended for better user experience
  static const Duration _sessionTimeout = Duration(days: 7); // Extended to match token expiry
  static const Duration _heartbeatInterval = Duration(hours: 1); // Reduced frequency for better performance
  static const Duration _inactivityWarning = Duration(days: 6, hours: 23); // Warn 1 hour before expiry

  SessionManagementService(
    this._apiClient,
    this._authProvider,
    this._retryService,
  );

  /// Initialize session management
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSessionState();
    _startSessionMonitoring();
  }

  /// Stream of session state changes
  Stream<SessionState> get sessionStateStream => _sessionStateController.stream;

  /// Current session state
  SessionState get currentState => _currentState;

  /// Current session ID
  String? get currentSessionId => _currentSessionId;

  /// Time since last activity
  Duration? get timeSinceLastActivity {
    if (_lastActivity == null) return null;
    return DateTime.now().difference(_lastActivity!);
  }

  /// Time until session expires
  Duration? get timeUntilExpiry {
    if (_lastActivity == null) return null;
    final expiryTime = _lastActivity!.add(_sessionTimeout);
    final now = DateTime.now();
    return expiryTime.isAfter(now) ? expiryTime.difference(now) : Duration.zero;
  }

  /// Start a new session after successful authentication
  Future<AppResult<void>> startSession({
    required String sessionId,
    required String userId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      _currentSessionId = sessionId;
      _lastActivity = DateTime.now();
      _currentState = SessionState.active;

      // Save session info locally
      await _saveSessionState();

      // Start monitoring
      _startSessionMonitoring();

      // Notify listeners
      _sessionStateController.add(_currentState);

      // Log session start
      await _logSessionEvent('session_start', metadata);

      return const AppResult.success(null);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Update session activity (call on user interactions)
  Future<void> updateActivity() async {
    if (_currentState != SessionState.active) return;

    _lastActivity = DateTime.now();
    await _saveSessionState();

    // Reset session timer
    _resetSessionTimer();
  }

  /// Perform secure logout
  Future<AppResult<void>> logout({
    LogoutType type = LogoutType.manual,
    bool invalidateAllSessions = false,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final logoutMetadata = {
        'logout_type': type.name,
        'invalidate_all_sessions': invalidateAllSessions,
        'session_id': _currentSessionId,
        'device_info': await _getDeviceInfo(),
        ...?metadata,
      };

      // Call logout API with retry
      final result = await _retryService.executeWithRetry<void>(
        operation: () => _performServerLogout(invalidateAllSessions),
        operationName: 'logout',
        config: RetryConfig.auth,
        metadata: logoutMetadata,
      );

      return result.when(
        success: (_) async {
          await _performLocalLogout(type, logoutMetadata);
          return const AppResult.success(null);
        },
        failure: (error) async {
          // Even if server logout fails, perform local logout for security
          await _performLocalLogout(type, logoutMetadata);
          
          // Return success but with warning about server logout failure
          return AppResult.failure(
            error.copyWith(
              message: 'Logged out locally, but server logout may have failed',
              messageAr: 'تم تسجيل الخروج محلياً، لكن تسجيل الخروج من الخادم قد يكون فشل',
              severity: AppErrorSeverity.medium,
            ),
          );
        },
        loading: () => const AppResult.loading(),
        cancelled: () => const AppResult.cancelled(),
      );
    } catch (e, stackTrace) {
      // Ensure local logout even if everything fails
      await _performLocalLogout(type, metadata);
      
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get all active sessions for the user
  Future<AppResult<List<SessionInfo>>> getActiveSessions() async {
    return _retryService.executeWithRetry<List<SessionInfo>>(
      operation: () async {
        final response = await _apiClient.get<Map<String, dynamic>>('/auth/sessions');
        if (!response.isSuccess) {
          throw Exception(response.error ?? 'Failed to get sessions');
        }
        final sessions = (response.data!['sessions'] as List)
            .map((json) => SessionInfo.fromJson(json))
            .toList();
        return sessions;
      },
      operationName: 'get_active_sessions',
      config: RetryConfig.auth,
    );
  }

  /// Terminate a specific session
  Future<AppResult<void>> terminateSession(String sessionId) async {
    return _retryService.executeWithRetry<void>(
      operation: () async {
        await _apiClient.delete('/auth/sessions/$sessionId');
      },
      operationName: 'terminate_session',
      config: RetryConfig.auth,
      metadata: {'target_session_id': sessionId},
    );
  }

  /// Terminate all other sessions (keep current session)
  Future<AppResult<void>> terminateAllOtherSessions() async {
    return _retryService.executeWithRetry<void>(
      operation: () async {
        await _apiClient.post('/auth/sessions/terminate-others', data: {});
      },
      operationName: 'terminate_other_sessions',
      config: RetryConfig.auth,
    );
  }

  /// Check if session is still valid on server
  Future<AppResult<bool>> validateSession() async {
    if (_currentSessionId == null) {
      return const AppResult.success(false);
    }

    return _retryService.executeWithRetry<bool>(
      operation: () async {
        final response = await _apiClient.get<Map<String, dynamic>>('/auth/sessions/validate');
        if (!response.isSuccess) {
          throw Exception(response.error ?? 'Failed to validate session');
        }
        return response.data!['valid'] == true;
      },
      operationName: 'validate_session',
      config: RetryConfig.auth,
    );
  }

  /// Perform server-side logout
  Future<void> _performServerLogout(bool invalidateAllSessions) async {
    final endpoint = invalidateAllSessions 
        ? '/auth/logout/all' 
        : '/auth/logout';
    
    await _apiClient.post(endpoint, data: {
      'session_id': _currentSessionId,
      'device_info': await _getDeviceInfo(),
    });
  }

  /// Perform local logout cleanup
  Future<void> _performLocalLogout(
    LogoutType type, 
    Map<String, dynamic>? metadata,
  ) async {
    // Stop monitoring
    _stopSessionMonitoring();

    // Clear tokens using Simple Supabase Auth
    await _authProvider.signOut();

    // Clear session data
    await _clearSessionState();

    // Update state
    _currentState = SessionState.terminated;
    _currentSessionId = null;
    _lastActivity = null;

    // Notify listeners
    _sessionStateController.add(_currentState);

    // Log logout event
    await _logSessionEvent('logout', {
      'logout_type': type.name,
      ...?metadata,
    });
  }

  /// Start session monitoring timers
  void _startSessionMonitoring() {
    _stopSessionMonitoring(); // Clear existing timers

    // Session timeout timer - check less frequently for better performance
    _sessionTimer = Timer.periodic(const Duration(hours: 1), (_) {
      _checkSessionTimeout();
    });

    // Heartbeat timer (keep session alive)
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (_) {
      _sendHeartbeat();
    });
  }

  /// Stop session monitoring
  void _stopSessionMonitoring() {
    _sessionTimer?.cancel();
    _heartbeatTimer?.cancel();
    _sessionTimer = null;
    _heartbeatTimer = null;
  }

  /// Reset session timer after activity
  void _resetSessionTimer() {
    // Session timer is checked periodically, no need to reset
    // Just update last activity time
  }

  /// Check for session timeout
  void _checkSessionTimeout() {
    if (_currentState != SessionState.active || _lastActivity == null) return;

    final timeSinceActivity = DateTime.now().difference(_lastActivity!);
    
    if (timeSinceActivity >= _sessionTimeout) {
      // Session expired
      _handleSessionExpiry();
    } else if (timeSinceActivity >= _inactivityWarning) {
      // Show inactivity warning
      _handleInactivityWarning();
    }
  }

  /// Handle session expiry
  void _handleSessionExpiry() async {
    _currentState = SessionState.expired;
    _sessionStateController.add(_currentState);

    // Perform automatic logout
    await logout(type: LogoutType.automatic);
  }

  /// Handle inactivity warning
  void _handleInactivityWarning() {
    // This could trigger a UI warning dialog
    // For now, just update the state
    if (_currentState == SessionState.active) {
      // Could add a warning state here
    }
  }

  /// Send heartbeat to keep session alive
  void _sendHeartbeat() async {
    if (_currentState != SessionState.active || _currentSessionId == null) return;

    try {
      await _apiClient.post('/auth/sessions/heartbeat', data: {
        'session_id': _currentSessionId,
        'last_activity': _lastActivity?.toIso8601String(),
      });
    } catch (e) {
      // Heartbeat failed - session might be invalid
      print('Heartbeat failed: $e');
    }
  }

  /// Save session state to local storage
  Future<void> _saveSessionState() async {
    final sessionData = {
      'session_id': _currentSessionId,
      'state': _currentState.name,
      'last_activity': _lastActivity?.toIso8601String(),
    };
    
    await _prefs.setString('session_state', jsonEncode(sessionData));
  }

  /// Load session state from local storage
  Future<void> _loadSessionState() async {
    final sessionJson = _prefs.getString('session_state');
    if (sessionJson == null) return;

    try {
      final sessionData = jsonDecode(sessionJson) as Map<String, dynamic>;
      _currentSessionId = sessionData['session_id'];
      _currentState = SessionState.values.firstWhere(
        (s) => s.name == sessionData['state'],
        orElse: () => SessionState.inactive,
      );
      
      if (sessionData['last_activity'] != null) {
        _lastActivity = DateTime.parse(sessionData['last_activity']);
      }

      // Check if loaded session is still valid
      if (_lastActivity != null) {
        final timeSinceActivity = DateTime.now().difference(_lastActivity!);
        if (timeSinceActivity >= _sessionTimeout) {
          await _clearSessionState();
          _currentState = SessionState.expired;
        }
      }
    } catch (e) {
      // Failed to load session state
      await _clearSessionState();
    }
  }

  /// Clear session state from local storage
  Future<void> _clearSessionState() async {
    await _prefs.remove('session_state');
    _currentSessionId = null;
    _lastActivity = null;
  }

  /// Get device information for session tracking
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    // This would typically use device_info_plus package
    return {
      'device_type': 'mobile', // or 'web', 'desktop'
      'device_name': 'User Device',
      'platform': 'flutter',
      'app_version': '1.0.0',
    };
  }

  /// Log session events for audit
  Future<void> _logSessionEvent(
    String eventType, 
    Map<String, dynamic>? metadata,
  ) async {
    try {
      await _apiClient.post('/auth/audit/session', data: {
        'event_type': eventType,
        'session_id': _currentSessionId,
        'timestamp': DateTime.now().toIso8601String(),
        'metadata': metadata,
      });
    } catch (e) {
      // Logging failed, but don't break the main flow
      print('Failed to log session event: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _stopSessionMonitoring();
    _sessionStateController.close();
  }
}

/// Riverpod provider for SessionManagementService
@riverpod
SessionManagementService sessionManagementService(
  Ref ref,
) {
  final apiClient = ref.read(simpleApiClientProvider);
  final authProvider = ref.read(simpleSupabaseAuthProvider.notifier);
  final retryService = ref.read(retryServiceProvider);
  
  final service = SessionManagementService(
    apiClient,
    authProvider,
    retryService,
  );
  
  // Initialize the service
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() => service.dispose());
  
  return service;
}
