import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../models/support_ticket_model.dart';
import 'support_provider.dart';

part 'support_ticket_provider.g.dart';

/// Support ticket actions provider - Clean implementation using Go backend
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
class SupportTicketActions extends _$SupportTicketActions {
  @override
  Future<void> build() async {
    // Initial state
  }

  /// Create a new support ticket
  Future<SupportTicketModel> createTicket({
    required String subject,
    required String description,
    required String category,
    required String priority,
  }) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      final currentUser = ref.read(currentUserProvider);
      
      if (currentUser?.id == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/support/tickets',
        data: {
          'subject': subject,
          'description': description,
          'category': category,
          'priority': priority,
        },
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to create support ticket: ${response.message}');
      }

      final ticketData = response.data!;
      
      final ticket = SupportTicketModel(
        id: ticketData['id'].toString(),
        userId: ticketData['user_id'] as String,
        subject: ticketData['subject'] as String,
        description: ticketData['description'] as String,
        status: _parseTicketStatus(ticketData['status'] as String?),
        priority: _parseTicketPriority(ticketData['priority'] as String?),
        category: _parseTicketCategory(ticketData['category'] as String?),
        unreadCount: ticketData['unread_count'] as int? ?? 0,
        createdAt: DateTime.parse(ticketData['created_at'] as String),
        lastMessageAt: ticketData['last_message_at'] != null
            ? DateTime.parse(ticketData['last_message_at'] as String)
            : null,
        lastMessageText: ticketData['last_message_text'] as String?,
        resolvedAt: ticketData['resolved_at'] != null
            ? DateTime.parse(ticketData['resolved_at'] as String)
            : null,
      );

      // Invalidate the tickets list to refresh it
      // ref.invalidate(supportTicketsProvider);
      
      return ticket;
    } catch (e) {
      throw Exception('خطأ في إنشاء تذكرة الدعم: $e');
    }
  }

  /// Send a message to a support ticket
  Future<SupportTicketMessageModel> sendMessage({
    required String ticketId,
    required String message,
  }) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);
      final currentUser = ref.read(currentUserProvider);
      
      if (currentUser?.id == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/support/tickets/$ticketId/messages',
        data: {
          'message': message,
        },
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to send message: ${response.message}');
      }

      final messageData = response.data!;
      
      final ticketMessage = SupportTicketMessageModel(
        id: messageData['id'].toString(),
        ticketId: messageData['ticket_id'] as String,
        senderId: messageData['sender_id'] as String,
        message: messageData['message'] as String,
        senderType: messageData['sender_type'] as String,
        sentAt: DateTime.parse(messageData['sent_at'] as String),
        createdAt: DateTime.parse(messageData['created_at'] as String),
      );

      // Invalidate the messages list to refresh it
      // ref.invalidate(supportTicketMessagesProvider(ticketId));
      
      return ticketMessage;
    } catch (e) {
      throw Exception('خطأ في إرسال الرسالة: $e');
    }
  }

  /// Close a support ticket
  Future<void> closeTicket(String ticketId) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);

      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/support/tickets/$ticketId/close',
      );

      if (!response.isSuccess) {
        throw Exception('Failed to close ticket: ${response.message}');
      }

      // Invalidate the tickets list to refresh it
      // ref.invalidate(supportTicketsProvider);
    } catch (e) {
      throw Exception('خطأ في إغلاق التذكرة: $e');
    }
  }

  /// Add a message to a support ticket (alias for sendMessage)
  Future<SupportTicketMessageModel> addMessage({
    required String ticketId,
    required String message,
  }) async {
    return sendMessage(ticketId: ticketId, message: message);
  }

  /// Update ticket status
  Future<void> updateTicketStatus({
    required String ticketId,
    required String status,
  }) async {
    try {
      final apiClient = ref.read(simpleApiClientProvider);

      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/support/tickets/$ticketId/status',
        data: {
          'status': status,
        },
      );

      if (!response.isSuccess) {
        throw Exception('Failed to update ticket status: ${response.message}');
      }

      // Invalidate the tickets list to refresh it
      // ref.invalidate(supportTicketsProvider);
      // ref.invalidate(supportTicketByIdProvider(ticketId));
    } catch (e) {
      throw Exception('خطأ في تحديث حالة التذكرة: $e');
    }
  }
}

/// Helper functions to parse enums
TicketStatus _parseTicketStatus(String? status) {
  switch (status) {
    case 'open':
      return TicketStatus.open;
    case 'in_progress':
      return TicketStatus.inProgress;
    case 'closed':
      return TicketStatus.closed;
    case 'urgent':
      return TicketStatus.urgent;
    case 'pending':
      return TicketStatus.pending;
    case 'resolved':
      return TicketStatus.resolved;
    default:
      return TicketStatus.open;
  }
}

TicketPriority _parseTicketPriority(String? priority) {
  switch (priority) {
    case 'high':
      return TicketPriority.high;
    case 'medium':
      return TicketPriority.medium;
    case 'low':
      return TicketPriority.low;
    default:
      return TicketPriority.medium;
  }
}

TicketCategory _parseTicketCategory(String? category) {
  switch (category) {
    case 'technical':
      return TicketCategory.technical;
    case 'billing':
      return TicketCategory.billing;
    case 'feature_request':
      return TicketCategory.featureRequest;
    case 'general':
      return TicketCategory.general;
    case 'bug_report':
      return TicketCategory.bugReport;
    case 'account':
      return TicketCategory.account;
    case 'orders':
      return TicketCategory.orders;
    case 'payments':
      return TicketCategory.payments;
    case 'seller':
      return TicketCategory.seller;
    case 'returns':
      return TicketCategory.returns;
    default:
      return TicketCategory.general;
  }
} 