import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/errors/app_error.dart';
import '../models/inventory_management_model.dart' hide InventoryStatus;
import '../models/seller_enums.dart';

part 'inventory_management_provider.g.dart';

/// مزود إدارة المخزون المتكامل
/// ✅ Updated to use SimpleApiClient (Forever Plan Architecture)
@riverpod
class InventoryManagement extends _$InventoryManagement {
  static final _logger = Logger();

  @override
  Future<List<SellerInventoryItem>> build() async {
    return _loadInventoryItems();
  }

  /// جلب عناصر المخزون للبائع
  /// Load inventory items for seller
  Future<List<SellerInventoryItem>> _loadInventoryItems() async {
    try {
      final currentUser = ref.read(currentUserProvider);
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated || currentUser?.id == null) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.get(
        '/api/v1/seller/inventory',
        queryParameters: {'seller_id': currentUser!.id},
      );

      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => SellerInventoryItem.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.e('خطأ في تحميل عناصر المخزون: $e');
      throw AppError.unexpected(
        message: 'Failed to load inventory items: $e',
        originalError: e,
      );
    }
  }

  /// تحديث كمية عنصر في المخزون
  /// Update inventory item quantity
  Future<void> updateItemQuantity(String itemId, int newQuantity) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      await apiClient.put(
        '/api/v1/seller/inventory/$itemId',
        data: {'quantity': newQuantity},
      );

      ref.invalidateSelf();
      _logger.i('تم تحديث كمية العنصر بنجاح');
    } catch (e) {
      _logger.e('خطأ في تحديث كمية العنصر: $e');
      throw AppError.unexpected(
        message: 'Failed to update item quantity: $e',
        originalError: e,
      );
    }
  }

  /// إضافة عنصر جديد للمخزون
  /// Add new item to inventory
  Future<void> addInventoryItem(SellerInventoryItem item) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      await apiClient.post(
        '/api/v1/seller/inventory',
        data: item.toJson(),
      );

      ref.invalidateSelf();
      _logger.i('تم إضافة عنصر جديد للمخزون بنجاح');
    } catch (e) {
      _logger.e('خطأ في إضافة عنصر للمخزون: $e');
      throw AppError.unexpected(
        message: 'Failed to add inventory item: $e',
        originalError: e,
      );
    }
  }

  /// حذف عنصر من المخزون
  /// Delete inventory item
  Future<void> deleteInventoryItem(String itemId) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      await apiClient.delete('/api/v1/seller/inventory/$itemId');

      ref.invalidateSelf();
      _logger.i('تم حذف العنصر من المخزون بنجاح');
    } catch (e) {
      _logger.e('خطأ في حذف العنصر من المخزون: $e');
      throw AppError.unexpected(
        message: 'Failed to delete inventory item: $e',
        originalError: e,
      );
    }
  }

  /// البحث في المخزون
  /// Search inventory
  Future<List<SellerInventoryItem>> searchInventory(String query) async {
    try {
      final currentUser = ref.read(currentUserProvider);
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated || currentUser?.id == null) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.get(
        '/api/v1/seller/inventory/search',
        queryParameters: {
          'query': query,
          'seller_id': currentUser!.id,
        },
      );

      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => SellerInventoryItem.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.e('خطأ في البحث في المخزون: $e');
      throw AppError.unexpected(
        message: 'Failed to search inventory: $e',
        originalError: e,
      );
    }
  }

  /// تحديث حالة عنصر المخزون
  /// Update inventory item status
  Future<void> updateItemStatus(String itemId, InventoryStatus status) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      await apiClient.put(
        '/api/v1/seller/inventory/$itemId/status',
        data: {'status': status.toString().split('.').last},
      );

      ref.invalidateSelf();
      _logger.i('تم تحديث حالة العنصر بنجاح');
    } catch (e) {
      _logger.e('خطأ في تحديث حالة العنصر: $e');
      throw AppError.unexpected(
        message: 'Failed to update item status: $e',
        originalError: e,
      );
    }
  }

  /// الحصول على إحصائيات المخزون
  /// Get inventory statistics
  Future<Map<String, dynamic>> getInventoryStats() async {
    try {
      final currentUser = ref.read(currentUserProvider);
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated || currentUser?.id == null) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.get(
        '/api/v1/seller/inventory/stats',
        queryParameters: {'seller_id': currentUser!.id},
      );

      return response.data as Map<String, dynamic>;
    } catch (e) {
      _logger.e('خطأ في تحميل إحصائيات المخزون: $e');
      throw AppError.unexpected(
        message: 'Failed to load inventory statistics: $e',
        originalError: e,
      );
    }
  }

  /// تحديث سعر عنصر المخزون
  /// Update inventory item price
  Future<void> updateItemPrice(String itemId, double newPrice) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      await apiClient.put(
        '/api/v1/seller/inventory/$itemId/price',
        data: {'price': newPrice},
      );

      ref.invalidateSelf();
      _logger.i('تم تحديث سعر العنصر بنجاح');
    } catch (e) {
      _logger.e('خطأ في تحديث سعر العنصر: $e');
      throw AppError.unexpected(
        message: 'Failed to update item price: $e',
        originalError: e,
      );
    }
  }

  /// جلب العناصر منخفضة المخزون
  /// Get low stock items
  Future<List<SellerInventoryItem>> getLowStockItems({int threshold = 5}) async {
    try {
      final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
      if (!authSystem.isAuthenticated || authSystem.currentUser?.id == null) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.get(
        '/api/v1/seller/inventory/low-stock',
        queryParameters: {
          'seller_id': authSystem.currentUser!.id,
          'threshold': threshold.toString(),
        },
      );

      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) => SellerInventoryItem.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.e('خطأ في تحميل العناصر منخفضة المخزون: $e');
      throw AppError.unexpected(
        message: 'Failed to load low stock items: $e',
        originalError: e,
      );
    }
  }

  /// التحقق من توفر كمية معينة
  /// Check if quantity is available
  Future<bool> checkAvailability(String itemId, int requestedQuantity) async {
    try {
      final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
      if (!authSystem.isAuthenticated || authSystem.currentUser?.id == null) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.get(
        '/api/v1/seller/inventory/$itemId/availability',
        queryParameters: {
          'seller_id': authSystem.currentUser!.id,
          'requested_quantity': requestedQuantity.toString(),
        },
      );

      return response.data['available'] as bool;
    } catch (e) {
      _logger.e('خطأ في التحقق من التوفر: $e');
      return false;
    }
  }

  /// حفظ نسخة احتياطية من بيانات المخزون
  /// Backup inventory data
  Future<void> backupInventoryData() async {
    try {
      final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
      if (!authSystem.isAuthenticated || authSystem.currentUser?.id == null) {
        throw AppError.authentication(message: 'User not authenticated');
      }

      final apiClient = ref.read(simpleApiClientProvider);
      await apiClient.post(
        '/api/v1/seller/inventory/backup',
        data: {'seller_id': authSystem.currentUser!.id},
      );

      _logger.i('تم حفظ النسخة الاحتياطية بنجاح');
    } catch (e) {
      _logger.e('خطأ في حفظ النسخة الاحتياطية: $e');
      throw AppError.unexpected(
        message: 'Failed to backup inventory data: $e',
        originalError: e,
      );
    }
  }
}

/// مزود تنبيهات المخزون
/// ✅ Updated to use SimpleApiClient (Forever Plan Architecture)
@riverpod
Future<List<Map<String, dynamic>>> stockAlerts(Ref ref) async {
  try {
    final currentUser = ref.read(currentUserProvider);
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated || currentUser?.id == null) {
      return [];
    }

    try {
      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.get(
        '/api/v1/seller/inventory/low-stock',
        queryParameters: {'seller_id': currentUser!.id},
      );

      return (response.data as List<dynamic>).cast<Map<String, dynamic>>();
    } catch (e) {
      throw AppError.unexpected(
        message: 'Failed to load stock alerts: $e',
        originalError: e,
      );
    }
  } catch (e) {
    throw AppError.unexpected(
      message: 'Failed to load stock alerts: $e',
      originalError: e,
    );
  }
}

/// مزود إحصائيات المخزون
/// ✅ Updated to use SimpleApiClient (Forever Plan Architecture)
@riverpod
Future<InventoryStats> inventoryStats(Ref ref) async {
  try {
    final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
    if (!authSystem.isAuthenticated || authSystem.currentUser?.id == null) {
      throw AppError.authentication(message: 'User not authenticated');
    }

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.get(
      '/api/v1/seller/inventory/statistics',
      queryParameters: {'seller_id': authSystem.currentUser!.id},
    );

    return InventoryStats.fromJson(response.data as Map<String, dynamic>);
  } catch (e) {
    throw AppError.unexpected(
      message: 'Failed to load inventory statistics: $e',
      originalError: e,
    );
  }
}
