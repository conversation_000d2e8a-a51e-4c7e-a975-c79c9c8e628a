import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:carnow/core/models/payment_method_model.dart';
import 'package:carnow/core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import 'dart:developer';

part 'payment_methods_provider.g.dart';

/// Provider لجلب طرق الدفع للمستخدم الحالي
/// ✅ Uses Go backend API instead of direct Supabase calls
/// Follows Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
Future<List<PaymentMethodModel>> userPaymentMethods(Ref ref) async {
  final apiClient = ref.watch(simpleApiClientProvider);
  final user = ref.watch(currentUserProvider);

  if (user == null) {
    throw Exception('المستخدم غير مسجل الدخول');
  }

  try {
    final response = await apiClient.getApi<List<dynamic>>(
      '/api/v1/payment-methods',
      queryParameters: {'user_id': user.id},
    );

    if (!response.isSuccess || response.data == null) {
      throw Exception('فشل في تحميل طرق الدفع: ${response.message}');
    }

    return response.data!
        .map((json) => PaymentMethodModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    log('Error fetching payment methods: $e');
    throw Exception('فشل في تحميل طرق الدفع: $e');
  }
}

/// Provider لإدارة طرق الدفع
/// ✅ Uses SimpleApiClient for ALL operations
@riverpod
class PaymentMethodsManager extends _$PaymentMethodsManager {
  @override
  FutureOr<List<PaymentMethodModel>> build() {
    return ref.watch(userPaymentMethodsProvider.future);
  }

  /// إضافة طريقة دفع جديدة
  Future<void> addPaymentMethod({
    required PaymentMethodType type,
    String? cardLastFour,
    String? cardBrand,
    int? expiryMonth,
    int? expiryYear,
    String? cardholderName,
    String? bankName,
    String? accountLastFour,
    String? walletProvider,
    String? walletEmail,
    bool isDefault = false,
  }) async {
    final apiClient = ref.read(simpleApiClientProvider);
    final user = ref.read(currentUserProvider);

    if (user == null) {
      throw Exception('المستخدم غير مسجل الدخول');
    }

    state = const AsyncLoading();

    try {
      final paymentMethodData = {
        'user_id': user.id,
        'type': type.name,
        'card_last_four': cardLastFour,
        'card_brand': cardBrand,
        'expiry_month': expiryMonth,
        'expiry_year': expiryYear,
        'cardholder_name': cardholderName,
        'bank_name': bankName,
        'account_last_four': accountLastFour,
        'wallet_provider': walletProvider,
        'wallet_email': walletEmail,
        'is_default': isDefault,
        'is_active': true,
        'is_verified': false,
      };

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/api/v1/payment-methods',
        data: paymentMethodData,
      );

      if (!response.isSuccess) {
        throw Exception('فشل في إضافة طريقة الدفع: ${response.message}');
      }

      // إعادة تحميل البيانات
      ref.invalidate(userPaymentMethodsProvider);
      state = await AsyncValue.guard(
        () => ref.read(userPaymentMethodsProvider.future),
      );
    } catch (e) {
      log('Error adding payment method: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  /// تحديث طريقة دفع موجودة
  Future<void> updatePaymentMethod({
    required String paymentMethodId,
    String? cardholderName,
    bool? isDefault,
  }) async {
    final apiClient = ref.read(simpleApiClientProvider);
    final user = ref.read(currentUserProvider);

    if (user == null) {
      throw Exception('المستخدم غير مسجل الدخول');
    }

    state = const AsyncLoading();

    try {
      final updateData = <String, dynamic>{};
      if (cardholderName != null) {
        updateData['cardholder_name'] = cardholderName;
      }
      if (isDefault != null) updateData['is_default'] = isDefault;

      if (updateData.isNotEmpty) {
        final response = await apiClient.putApi<Map<String, dynamic>>(
          '/api/v1/payment-methods/$paymentMethodId',
          data: updateData,
        );

        if (!response.isSuccess) {
          throw Exception('فشل في تحديث طريقة الدفع: ${response.message}');
        }
      }

      // إعادة تحميل البيانات
      ref.invalidate(userPaymentMethodsProvider);
      state = await AsyncValue.guard(
        () => ref.read(userPaymentMethodsProvider.future),
      );
    } catch (e) {
      log('Error updating payment method: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  /// حذف طريقة دفع
  Future<void> deletePaymentMethod(String paymentMethodId) async {
    final apiClient = ref.read(simpleApiClientProvider);

    state = const AsyncLoading();

    try {
      final response = await apiClient.deleteApi(
        '/api/v1/payment-methods/$paymentMethodId',
      );

      if (!response.isSuccess) {
        throw Exception('فشل في حذف طريقة الدفع: ${response.message}');
      }

      // إعادة تحميل البيانات
      ref.invalidate(userPaymentMethodsProvider);
      state = await AsyncValue.guard(
        () => ref.read(userPaymentMethodsProvider.future),
      );
    } catch (e) {
      log('Error deleting payment method: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  /// تعيين طريقة دفع كافتراضية
  Future<void> setAsDefault(String paymentMethodId) async {
    await updatePaymentMethod(
      paymentMethodId: paymentMethodId,
      isDefault: true,
    );
  }
}

/// Provider للحصول على طريقة الدفع الافتراضية
/// ✅ Uses payment methods from Go backend API
@riverpod
Future<PaymentMethodModel?> defaultPaymentMethod(Ref ref) async {
  final paymentMethods = await ref.watch(userPaymentMethodsProvider.future);

  try {
    return paymentMethods.firstWhere((method) => method.isDefault);
  } catch (e) {
    return null; // لا توجد طريقة دفع افتراضية
  }
}
