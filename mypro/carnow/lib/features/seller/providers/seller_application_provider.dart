import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/utils/unified_logger.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../models/seller_application_model.dart';
import '../models/seller_enums.dart';
import '../services/seller_api_service.dart';

part 'seller_application_provider.g.dart';

@riverpod
class SellerApplication extends _$SellerApplication {
  @override
  FutureOr<SellerApplicationModel?> build() async =>
      _getCurrentUserApplication();

  Future<SellerApplicationModel?> _getCurrentUserApplication() async {
    final user = ref.read(currentUserProvider);
    if (user == null) return null;

    try {
      final sellerApiService = ref.read(sellerApiServiceProvider);
      return await sellerApiService.getCurrentUserApplication();
    } catch (e) {
      UnifiedLogger.error('Failed to fetch seller application: $e');
      return null;
    }
  }

  Future<void> submitApplication(SellerApplicationModel application) async {
    state = const AsyncValue.loading();

    try {
      final user = ref.read(currentUserProvider);
      if (user == null) throw Exception('User not authenticated');

      // التحقق من عدم وجود طلب سابق معلق
      final existingApplication = await _getCurrentUserApplication();
      if (existingApplication != null &&
          existingApplication.status == ApplicationStatus.pending) {
        throw Exception('لديك طلب معلق بالفعل');
      }

      final applicationData = application.toJson();
      applicationData['user_id'] = user.id;
      applicationData.remove('id'); // Let database generate ID
      applicationData.remove('metadata'); // Remove metadata as it's not part of the database table

      final sellerApiService = ref.read(sellerApiServiceProvider);
      final newApplication = await sellerApiService.submitApplication(applicationData);

      state = AsyncValue.data(newApplication);
    } catch (e) {
      UnifiedLogger.error('Error submitting application: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> updateApplication(SellerApplicationModel application) async {
    state = const AsyncValue.loading();

    try {
      final user = ref.read(currentUserProvider);
      if (user == null) throw Exception('User not authenticated');

      if (application.userId != user.id) {
        throw Exception('لا يمكنك تعديل طلب شخص آخر');
      }

      if (application.status != ApplicationStatus.pending) {
        throw Exception('لا يمكن تعديل الطلب بعد بدء المراجعة');
      }

      // For now, we'll refresh the application data since the Go backend
      // will handle the update logic
      final updatedApplication = await _getCurrentUserApplication();
      if (updatedApplication != null) {
        state = AsyncValue.data(updatedApplication);
      }


    } catch (e) {
      UnifiedLogger.error('Error updating application: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

@riverpod
Future<bool> canApplyAsSeller(Ref ref) async {
  try {
    final sellerApiService = ref.read(sellerApiServiceProvider);
    return await sellerApiService.canApplyAsSeller();
  } catch (e) {
    UnifiedLogger.error('Error checking seller eligibility: $e');
    return false;
  }
}



@riverpod
Future<List<Map<String, dynamic>>> sellerApplicationLogs(
  Ref ref,
  String applicationId,
) async {
  try {
    final sellerApiService = ref.read(sellerApiServiceProvider);
    return await sellerApiService.getApplicationLogs(applicationId);
  } catch (e) {
    UnifiedLogger.error('Error fetching application logs: $e');
    throw Exception('Failed to fetch application logs: $e');
  }
}
