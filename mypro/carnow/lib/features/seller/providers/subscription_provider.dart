import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

part 'subscription_provider.g.dart';

final _logger = Logger('SubscriptionProvider');

/// Subscription Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses Go Backend API ONLY for subscriptions
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns

/// Provider for current seller subscription
@riverpod
class SellerSubscriptionProvider extends _$SellerSubscriptionProvider {
  @override
  Future<SellerSubscription?> build() async {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);
    if (!isAuthenticated) return null;

    return _fetchCurrentSubscription();
  }

  Future<SellerSubscription?> _fetchCurrentSubscription() async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        _logger.warning('User not authenticated');
        return null;
      }

      _logger.info('Fetching current subscription from Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/seller/subscription/current',
      );

      if (!response.isSuccess || response.data == null) {
        _logger.info('No active subscription found');
        return null;
      }

      final subscription = SellerSubscription.fromJson(response.data!);
      _logger.info('Current subscription fetched successfully: ${subscription.tier}');
      return subscription;
    } catch (e, st) {
      _logger.severe('Error fetching current subscription', e, st);
      return null;
    }
  }

  /// Subscribe to a plan through Go backend
  Future<SubscriptionResult> subscribeToPlan({
    required SubscriptionPlan plan,
    required BillingCycle billingCycle,
    String? paymentMethodId,
  }) async {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    state = const AsyncValue.loading();

    try {
      _logger.info('Subscribing to plan ${plan.id} via Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final requestData = {
        'plan_id': plan.id,
        'tier': plan.tier.name,
        'billing_cycle': billingCycle.name,
        'payment_method_id': paymentMethodId,
      };

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/seller/subscription/subscribe',
        data: requestData,
      );

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to subscribe to plan: ${response.message}');
        state = AsyncValue.error(Exception(response.message), StackTrace.current);
        return SubscriptionResult(
          success: false,
          message: response.message ?? 'فشل في الاشتراك',
        );
      }

      // Refresh the subscription state
      ref.invalidateSelf();

      _logger.info('Successfully subscribed to plan ${plan.id}');
      
      final resultData = response.data!;
      return SubscriptionResult(
        success: true,
        message: resultData['message'] as String? ?? 'تم الاشتراك بنجاح',
        subscriptionId: resultData['subscription_id'] as String?,
        trialEndDate: resultData['trial_end_date'] != null 
            ? DateTime.tryParse(resultData['trial_end_date'] as String)
            : null,
      );
    } catch (e, st) {
      _logger.severe('Error subscribing to plan: ${plan.id}', e, st);
      state = AsyncValue.error(e, st);
      return SubscriptionResult(
        success: false,
        message: 'حدث خطأ أثناء الاشتراك',
      );
    }
  }

  /// Cancel subscription gracefully through Go backend
  Future<SubscriptionCancellationResult> cancelSubscriptionGracefully({
    String? cancellationReason,
  }) async {
    final currentSubscription = await future;
    if (currentSubscription == null) {
      throw Exception('لا يوجد اشتراك نشط للإلغاء');
    }

    state = const AsyncValue.loading();

    try {
      _logger.info('Gracefully canceling subscription via Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final requestData = {
        'cancellation_reason': cancellationReason,
        'cancellation_type': 'graceful',
      };

      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/seller/subscription/cancel',
        data: requestData,
      );

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to cancel subscription: ${response.message}');
        state = AsyncValue.error(Exception(response.message), StackTrace.current);
        throw Exception(response.message ?? 'فشل في إلغاء الاشتراك');
      }

      // Refresh the subscription state
      ref.invalidateSelf();

      final resultData = response.data!;
      _logger.info('Successfully canceled subscription gracefully');

      return SubscriptionCancellationResult(
        success: true,
        message: resultData['message'] as String? ?? 'تم إلغاء الاشتراك بنجاح',
        daysRemaining: resultData['days_remaining'] as int? ?? 0,
        endDate: resultData['end_date'] != null 
            ? DateTime.tryParse(resultData['end_date'] as String) ?? DateTime.now()
            : DateTime.now(),
        canManageExistingListings: resultData['can_manage_existing'] as bool? ?? false,
        canCreateNewListings: resultData['can_create_new'] as bool? ?? false,
      );
    } catch (e, st) {
      _logger.severe('Error canceling subscription gracefully', e, st);
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  /// Cancel subscription immediately through Go backend (admin only)
  Future<void> cancelSubscription() async {
    final currentSubscription = await future;
    if (currentSubscription == null) return;

    state = const AsyncValue.loading();

    try {
      _logger.info('Canceling subscription immediately via Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final requestData = {
        'cancellation_type': 'immediate',
      };

      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/seller/subscription/cancel',
        data: requestData,
      );

      if (!response.isSuccess) {
        _logger.warning('Failed to cancel subscription: ${response.message}');
        state = AsyncValue.error(Exception(response.message), StackTrace.current);
        throw Exception(response.message ?? 'فشل في إلغاء الاشتراك');
      }

      // Refresh the subscription state
      ref.invalidateSelf();

      _logger.info('Successfully canceled subscription immediately');
    } catch (e, st) {
      _logger.severe('Error canceling subscription immediately', e, st);
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  /// Update auto renewal setting through Go backend
  Future<void> updateAutoRenewal(bool autoRenewal) async {
    final currentSubscription = await future;
    if (currentSubscription == null) return;

    try {
      _logger.info('Updating auto renewal to $autoRenewal via Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final requestData = {
        'auto_renewal': autoRenewal,
      };

      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/seller/subscription/auto-renewal',
        data: requestData,
      );

      if (!response.isSuccess) {
        _logger.warning('Failed to update auto renewal: ${response.message}');
        throw Exception(response.message ?? 'فشل في تحديث التجديد التلقائي');
      }

      // Refresh the subscription state
      ref.invalidateSelf();

      _logger.info('Successfully updated auto renewal to $autoRenewal');
    } catch (e, st) {
      _logger.severe('Error updating auto renewal', e, st);
      rethrow;
    }
  }

  /// Update payment method through Go backend
  Future<void> updatePaymentMethod(String paymentMethodId) async {
    final currentSubscription = await future;
    if (currentSubscription == null) return;

    try {
      _logger.info('Updating payment method via Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final requestData = {
        'payment_method_id': paymentMethodId,
      };

      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/seller/subscription/payment-method',
        data: requestData,
      );

      if (!response.isSuccess) {
        _logger.warning('Failed to update payment method: ${response.message}');
        throw Exception(response.message ?? 'فشل في تحديث طريقة الدفع');
      }

      // Refresh the subscription state
      ref.invalidateSelf();

      _logger.info('Successfully updated payment method');
    } catch (e, st) {
      _logger.severe('Error updating payment method', e, st);
      rethrow;
    }
  }

  /// Upgrade or downgrade subscription plan
  Future<bool> upgradePlan({
    required SubscriptionPlan newPlan,
    bool immediate = false,
  }) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      _logger.info('Upgrading subscription plan to: ${newPlan.id}');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/subscription/upgrade',
        data: {
          'plan_id': newPlan.id,
          'immediate': immediate,
        },
      );

      if (response.isSuccess) {
        _logger.info('Subscription plan upgraded successfully');
        // Refresh the subscription data
        ref.invalidateSelf();
        return true;
      } else {
        final errorMessage = response.message ?? 'Failed to upgrade subscription plan';
        _logger.severe('Subscription upgrade failed: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      _logger.severe('Error upgrading subscription plan: $e');
      rethrow;
    }
  }

  /// Refresh subscription data
  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

/// Provider for subscription billing history
@riverpod
Future<List<SubscriptionBilling>> subscriptionBillingHistory(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch billing history');
      return [];
    }

    _logger.info('Fetching billing history from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<List<dynamic>>(
      '/seller/subscription/billing-history',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch billing history: ${response.message}');
      return [];
    }

    final billingHistory = response.data!
        .cast<Map<String, dynamic>>()
        .map((data) => SubscriptionBilling.fromJson(data))
        .toList();

    _logger.info('Billing history fetched successfully: ${billingHistory.length} items');
    return billingHistory;
  } catch (e) {
    _logger.severe('Error fetching billing history: $e');
    return [];
  }
}

/// Provider for available subscription plans
@riverpod
Future<List<SubscriptionPlan>> availableSubscriptionPlans(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching available subscription plans from Go backend');
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/subscription/plans/available',
    );
    
    if (response.isSuccess && response.data != null) {
      final plansList = response.data!['plans'] as List<dynamic>? ?? [];
      return plansList
          .map((json) => SubscriptionPlan.fromJson(json as Map<String, dynamic>))
          .toList();
    }
    
    _logger.warning('No subscription plans data received');
    return [];
  } catch (e) {
    _logger.severe('Error fetching available subscription plans: $e');
    return [];
  }
}

/// Provider for monthly quota usage (typed)
@riverpod
Future<MonthlyQuotaUsage?> monthlyQuotaUsage(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch quota usage');
      return null;
    }

    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching monthly quota usage from Go backend');
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/subscription/usage/monthly',
    );
    
    if (response.isSuccess && response.data != null) {
      final usageData = response.data!['usage'] as Map<String, dynamic>?;
      if (usageData != null) {
        return MonthlyQuotaUsage.fromJson(usageData);
      }
    }
    
    _logger.warning('No quota usage data received');
    return null;
  } catch (e) {
    _logger.severe('Error fetching monthly quota usage: $e');
    return null;
  }
}

/// Provider for subscription usage statistics
@riverpod
Future<Map<String, dynamic>> subscriptionUsage(Ref ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.warning('User not authenticated, cannot fetch usage stats');
      return <String, dynamic>{};
    }

    _logger.info('Fetching subscription usage from Go backend');

    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/seller/subscription/usage',
    );

    if (!response.isSuccess || response.data == null) {
      _logger.warning('Failed to fetch subscription usage: ${response.message}');
      return <String, dynamic>{};
    }

    _logger.info('Subscription usage fetched successfully');
    return response.data!;
  } catch (e) {
    _logger.severe('Error fetching subscription usage: $e');
    return <String, dynamic>{};
  }
}

// =============================================================================
// Result Models
// =============================================================================

class SubscriptionResult {
  final bool success;
  final String message;
  final String? subscriptionId;
  final DateTime? trialEndDate;

  const SubscriptionResult({
    required this.success,
    required this.message,
    this.subscriptionId,
    this.trialEndDate,
  });
}

class SubscriptionCancellationResult {
  final bool success;
  final String message;
  final int daysRemaining;
  final DateTime endDate;
  final bool canManageExistingListings;
  final bool canCreateNewListings;

  const SubscriptionCancellationResult({
    required this.success,
    required this.message,
    required this.daysRemaining,
    required this.endDate,
    required this.canManageExistingListings,
    required this.canCreateNewListings,
  });
}
