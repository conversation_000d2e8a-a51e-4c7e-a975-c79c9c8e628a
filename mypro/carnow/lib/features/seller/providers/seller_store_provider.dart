import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../models/seller_store_model.dart';

final _logger = Logger('SellerStoreProvider');

/// Seller Store Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses SimpleApiClient ONLY
/// ✅ ALL business logic in Go backend
/// ✅ NO direct Supabase calls

/// Provider that fetches current user's seller store from Go backend
final sellerStoreProvider = FutureProvider<SellerStoreModel?>((ref) async {
  try {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      _logger.info('User not authenticated, cannot fetch store');
      return null;
    }

    _logger.info('Fetching seller store from Go backend');
    
    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.getApi<Map<String, dynamic>>('/seller/store');

    if (!response.isSuccess || response.data == null) {
      _logger.info('No seller store found');
      return null;
    }

    final store = SellerStoreModel.fromJson(response.data!);
    _logger.info('Seller store fetched successfully');
    return store;
  } catch (e) {
    _logger.severe('Error fetching seller store: $e');
    return null;
  }
});

/// Notifier for managing seller store through Go backend
class SellerStoreNotifier extends AsyncNotifier<SellerStoreModel?> {
  @override
  Future<SellerStoreModel?> build() async {
    // Initial state is null until explicitly loaded
    return null;
  }

  /// Load store for current authenticated user
  Future<void> loadStore() async {
    state = const AsyncValue.loading();

    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      _logger.info('Loading seller store from Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.getApi<Map<String, dynamic>>('/seller/store');

      if (!response.isSuccess || response.data == null) {
        _logger.info('No seller store found');
        state = const AsyncValue.data(null);
        return;
      }

      final store = SellerStoreModel.fromJson(response.data!);
      state = AsyncValue.data(store);
      _logger.info('Seller store loaded successfully');
    } catch (e) {
      _logger.severe('Error loading seller store: $e');
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Create a new store through Go backend
  Future<SellerStoreModel> createStore(SellerStoreModel store) async {
    try {
      _logger.info('Creating seller store through Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/seller/store',
        data: store.toJson(),
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to create store');
      }

      final createdStore = SellerStoreModel.fromJson(response.data!);
      state = AsyncValue.data(createdStore);
      _logger.info('Seller store created successfully');
      return createdStore;
    } catch (e) {
      _logger.severe('Error creating store: $e');
      rethrow;
    }
  }

  /// Update an existing store through Go backend
  Future<SellerStoreModel> updateStore(SellerStoreModel store) async {
    if (store.id == null) {
      throw ArgumentError('Cannot update store with null ID');
    }

    try {
      _logger.info('Updating seller store through Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/seller/store',
        data: store.toJson(),
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to update store');
      }

      final updatedStore = SellerStoreModel.fromJson(response.data!);
      state = AsyncValue.data(updatedStore);
      _logger.info('Seller store updated successfully');
      return updatedStore;
    } catch (e) {
      _logger.severe('Error updating store: $e');
      rethrow;
    }
  }

  /// Delete store through Go backend
  Future<void> deleteStore() async {
    try {
      _logger.info('Deleting seller store through Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.deleteApi('/seller/store');

      if (!response.isSuccess) {
        throw Exception('Failed to delete store');
      }

      state = const AsyncValue.data(null);
      _logger.info('Seller store deleted successfully');
    } catch (e) {
      _logger.severe('Error deleting store: $e');
      rethrow;
    }
  }
}

/// Provider for the seller store notifier
final sellerStoreNotifierProvider =
    AsyncNotifierProvider<SellerStoreNotifier, SellerStoreModel?>(
      SellerStoreNotifier.new,
    );
