import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/utils/unified_logger.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../services/notifications_api_service.dart';
import '../models/seller_notification_model.dart';

part 'seller_notifications_async_provider.g.dart';

// Note: We'll need to generate the Riverpod provider using @riverpod later
// Currently using the manual approach for immediate testing

@riverpod
class SellerNotificationsAsync extends _$SellerNotificationsAsync {
  Timer? _refreshTimer;

  @override
  Future<List<SellerNotification>> build() async {
    // Set up auto-refresh every 30 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      ref.invalidateSelf();
    });

    // Clean up when provider is disposed
    ref.onDispose(() {
      _disposeResources();
    });

    final authData = ref.read(currentUserProvider);
    if (authData == null) {
      throw Exception('User not authenticated');
    }

    final apiService = ref.read(notificationsApiServiceProvider);
    return apiService.getNotifications(authData.id);
  }

  /// Clean up resources
  void _disposeResources() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// إعادة تعيين المؤشر للإشعارات الجديدة
  Future<void> markAllAsRead() async {
    try {
      final authData = ref.read(currentUserProvider);
      if (authData == null) {
        UnifiedLogger.error('User not authenticated for markAllAsRead');
        return;
      }

      final apiService = ref.read(notificationsApiServiceProvider);
      await apiService.markAllAsRead(authData.id);

      // تحديث قائمة الإشعارات
      ref.invalidateSelf();
    } catch (e, stackTrace) {
      UnifiedLogger.error('Error marking notifications as read', error: e, stackTrace: stackTrace);
    }
  }

  /// إعادة تحميل الإشعارات
  Future<void> refresh() async {
    try {
      ref.invalidateSelf();
    } catch (e, stackTrace) {
      UnifiedLogger.error('Error refreshing notifications', error: e, stackTrace: stackTrace);
    }
  }

  /// إعلان إشعار محدد كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      final authData = ref.read(currentUserProvider);
      if (authData == null) {
        UnifiedLogger.error('User not authenticated for markAsRead');
        return;
      }

      final apiService = ref.read(notificationsApiServiceProvider);
      await apiService.markAsRead(notificationId);

      // تحديث قائمة الإشعارات
      ref.invalidateSelf();
    } catch (e, stackTrace) {
      UnifiedLogger.error('Error marking notification as read', error: e, stackTrace: stackTrace);
    }
  }

  /// حذف إشعار محدد
  Future<void> deleteNotification(String notificationId) async {
    try {
      final authData = ref.read(currentUserProvider);
      if (authData == null) {
        UnifiedLogger.error('User not authenticated for deleteNotification');
        return;
      }

      final apiService = ref.read(notificationsApiServiceProvider);
      await apiService.deleteNotification(notificationId);

      // تحديث قائمة الإشعارات
      ref.invalidateSelf();
    } catch (e, stackTrace) {
      UnifiedLogger.error('Error deleting notification', error: e, stackTrace: stackTrace);
    }
  }
}

/// Provider for unread notifications count
@riverpod
int unreadNotificationsCount(Ref ref) {
  final notificationsAsync = ref.watch(sellerNotificationsAsyncProvider);
  return notificationsAsync.when(
    data: (notifications) => notifications.where((n) => !n.isRead).length,
    loading: () => 0,
    error: (error, st) => 0,
  );
}



