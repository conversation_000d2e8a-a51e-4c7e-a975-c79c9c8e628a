import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';

import '../models/seller_profile_model.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

part 'subscription_request_provider.g.dart';

final _logger = Logger('SellerSubscriptionRequestProvider');

/// Seller Subscription Request Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses Go Backend API ONLY for subscription requests
/// ✅ NO direct Supabase calls
/// ✅ Real data only from backend
/// ✅ Clean async/await patterns
/// ✅ Comprehensive error handling

// =============================================================================
// SUBSCRIPTION REQUEST STATE
// =============================================================================

/// State for seller subscription request operations
class SellerSubscriptionRequestState {
  final bool isLoading;
  final EnhancedSubscriptionRequest? currentRequest;
  final String? errorMessage;
  final String? errorMessageAr;
  final String? successMessage;
  final String? successMessageAr;
  final DateTime? lastUpdated;

  const SellerSubscriptionRequestState({
    this.isLoading = false,
    this.currentRequest,
    this.errorMessage,
    this.errorMessageAr,
    this.successMessage,
    this.successMessageAr,
    this.lastUpdated,
  });

  SellerSubscriptionRequestState copyWith({
    bool? isLoading,
    EnhancedSubscriptionRequest? currentRequest,
    String? errorMessage,
    String? errorMessageAr,
    String? successMessage,
    String? successMessageAr,
    DateTime? lastUpdated,
  }) {
    return SellerSubscriptionRequestState(
      isLoading: isLoading ?? this.isLoading,
      currentRequest: currentRequest ?? this.currentRequest,
      errorMessage: errorMessage ?? this.errorMessage,
      errorMessageAr: errorMessageAr ?? this.errorMessageAr,
      successMessage: successMessage ?? this.successMessage,
      successMessageAr: successMessageAr ?? this.successMessageAr,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Clear error and success messages
  SellerSubscriptionRequestState clearMessages() {
    return copyWith(
      errorMessage: null,
      errorMessageAr: null,
      successMessage: null,
      successMessageAr: null,
      lastUpdated: DateTime.now(),
    );
  }

  /// Check if there's a pending request
  bool get hasPendingRequest => 
      currentRequest?.status == SubscriptionRequestStatus.pending ||
      currentRequest?.status == SubscriptionRequestStatus.underReview;

  /// Check if request was approved
  bool get isApproved => currentRequest?.status == SubscriptionRequestStatus.approved;

  /// Check if request was rejected
  bool get isRejected => currentRequest?.status == SubscriptionRequestStatus.rejected;
}

// =============================================================================
// MAIN PROVIDER
// =============================================================================

/// Provider for seller subscription request operations
@riverpod
class SellerSubscriptionRequestProvider extends _$SellerSubscriptionRequestProvider {
  @override
  SellerSubscriptionRequestState build() {
    // Initialize and fetch current request if user is authenticated
    final isAuthenticated = ref.watch(isAuthenticatedProvider);
    if (isAuthenticated) {
      _fetchCurrentRequest();
    }
    
    return const SellerSubscriptionRequestState();
  }

  /// Fetch current subscription request from backend
  Future<void> _fetchCurrentRequest() async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        _logger.warning('User not authenticated');
        return;
      }

      _logger.info('Fetching current subscription request from Go backend');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/seller/subscription/request/current',
      );

      if (response.isSuccess && response.data != null) {
        final request = EnhancedSubscriptionRequest.fromJson(response.data!);
        state = state.copyWith(
          currentRequest: request,
          lastUpdated: DateTime.now(),
        );
        _logger.info('Current subscription request fetched successfully: ${request.status}');
      } else {
        _logger.info('No current subscription request found');
        state = state.copyWith(
          currentRequest: null,
          lastUpdated: DateTime.now(),
        );
      }
    } catch (e, st) {
      _logger.severe('Error fetching current subscription request', e, st);
      state = state.copyWith(
        errorMessage: 'Failed to fetch subscription request',
        errorMessageAr: 'فشل في جلب طلب الاشتراك',
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// Submit a new subscription request
  Future<bool> submitRequest({
    required SubscriptionPlan plan,
    required BillingCycle billingCycle,
    required Map<String, dynamic> sellerInfo,
    String? paymentMethodId,
  }) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
        errorMessageAr: null,
        successMessage: null,
        successMessageAr: null,
      );

      _logger.info('Submitting subscription request for plan: ${plan.id}');

      final apiClient = ref.read(simpleApiClientProvider);
      final requestData = {
        'plan_id': plan.id,
        'requested_tier_name': plan.tier.name,
        'billing_cycle_name': billingCycle.name,
        'requested_price_ld': billingCycle == BillingCycle.monthly ? plan.monthlyPriceLD : plan.yearlyPriceLD,
        'payment_method_id': paymentMethodId,
        'seller_info': sellerInfo,
      };

      final response = await apiClient.postApi<Map<String, dynamic>>(
        '/seller/subscription/request',
        data: requestData,
      );

      if (response.isSuccess && response.data != null) {
        final newRequest = EnhancedSubscriptionRequest.fromJson(response.data!);
        
        state = state.copyWith(
          isLoading: false,
          currentRequest: newRequest,
          successMessage: 'Subscription request submitted successfully',
          successMessageAr: 'تم إرسال طلب الاشتراك بنجاح',
          lastUpdated: DateTime.now(),
        );

        _logger.info('Subscription request submitted successfully: ${newRequest.id}');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: response.message ?? 'Failed to submit subscription request',
          errorMessageAr: 'فشل في إرسال طلب الاشتراك',
          lastUpdated: DateTime.now(),
        );
        return false;
      }
    } catch (e, st) {
      _logger.severe('Error submitting subscription request', e, st);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error submitting subscription request: ${e.toString()}',
        errorMessageAr: 'خطأ في إرسال طلب الاشتراك',
        lastUpdated: DateTime.now(),
      );
      return false;
    }
  }

  /// Cancel current subscription request
  Future<bool> cancelRequest() async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      if (state.currentRequest == null) {
        throw Exception('No current request to cancel');
      }

      state = state.copyWith(isLoading: true);

      _logger.info('Cancelling subscription request: ${state.currentRequest!.id}');

      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.putApi<Map<String, dynamic>>(
        '/seller/subscription/request/cancel',
        data: {},
      );

      if (response.isSuccess) {
        // Refresh current request
        await _fetchCurrentRequest();
        
        state = state.copyWith(
          isLoading: false,
          successMessage: 'Subscription request cancelled successfully',
          successMessageAr: 'تم إلغاء طلب الاشتراك بنجاح',
          lastUpdated: DateTime.now(),
        );

        _logger.info('Subscription request cancelled successfully');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: response.message ?? 'Failed to cancel subscription request',
          errorMessageAr: 'فشل في إلغاء طلب الاشتراك',
          lastUpdated: DateTime.now(),
        );
        return false;
      }
    } catch (e, st) {
      _logger.severe('Error cancelling subscription request', e, st);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error cancelling subscription request: ${e.toString()}',
        errorMessageAr: 'خطأ في إلغاء طلب الاشتراك',
        lastUpdated: DateTime.now(),
      );
      return false;
    }
  }

  /// Refresh current request data
  Future<void> refresh() async {
    await _fetchCurrentRequest();
  }

  /// Clear all messages (success and error)
  void clearMessages() {
    state = state.clearMessages();
  }

  /// Retry last operation
  Future<void> retryOperation() async {
    _logger.info('Retrying last operation');
    await _fetchCurrentRequest();
  }
}

// =============================================================================
// CONVENIENCE PROVIDERS
// =============================================================================

/// Provider for checking if user has pending subscription request
@riverpod
bool hasPendingSubscriptionRequest(Ref ref) {
  final requestState = ref.watch(sellerSubscriptionRequestProviderProvider);
  return requestState.hasPendingRequest;
}

/// Provider for current subscription request
@riverpod
EnhancedSubscriptionRequest? currentSubscriptionRequest(Ref ref) {
  final requestState = ref.watch(sellerSubscriptionRequestProviderProvider);
  return requestState.currentRequest;
}

/// Provider for subscription request loading state
@riverpod
bool isSubscriptionRequestLoading(Ref ref) {
  final requestState = ref.watch(sellerSubscriptionRequestProviderProvider);
  return requestState.isLoading;
}

/// Provider for subscription request error message
@riverpod
String? subscriptionRequestError(Ref ref) {
  final requestState = ref.watch(sellerSubscriptionRequestProviderProvider);
  return requestState.errorMessage;
}

/// Provider for subscription request error message in Arabic
@riverpod
String? subscriptionRequestErrorAr(Ref ref) {
  final requestState = ref.watch(sellerSubscriptionRequestProviderProvider);
  return requestState.errorMessageAr;
}

/// Provider for subscription request success message
@riverpod
String? subscriptionRequestSuccess(Ref ref) {
  final requestState = ref.watch(sellerSubscriptionRequestProviderProvider);
  return requestState.successMessage;
}

/// Provider for subscription request success message in Arabic
@riverpod
String? subscriptionRequestSuccessAr(Ref ref) {
  final requestState = ref.watch(sellerSubscriptionRequestProviderProvider);
  return requestState.successMessageAr;
}
