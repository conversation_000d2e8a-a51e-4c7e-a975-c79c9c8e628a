import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/models/subscription_request.dart';
import '../../../core/models/subscription_response.dart';
import '../../../core/models/subscription_error.dart';
import '../../../core/error/retry_service.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

part 'subscription_service.g.dart';

/// Result type for subscription operations using core models
sealed class SubscriptionResult<T> {
  const SubscriptionResult();

  const factory SubscriptionResult.success(T data) = SubscriptionSuccess<T>;
  const factory SubscriptionResult.failure(SubscriptionError error) =
      SubscriptionFailure<T>;

  R when<R>({
    required R Function(T data) success,
    required R Function(SubscriptionError error) failure,
  }) {
    if (this is SubscriptionSuccess<T>) {
      return success((this as SubscriptionSuccess<T>).data);
    } else if (this is SubscriptionFailure<T>) {
      return failure((this as SubscriptionFailure<T>).error);
    }
    throw StateError('Unknown SubscriptionResult type');
  }
}

class SubscriptionSuccess<T> extends SubscriptionResult<T> {
  const SubscriptionSuccess(this.data);
  final T data;
}

class SubscriptionFailure<T> extends SubscriptionResult<T> {
  const SubscriptionFailure(this.error);
  final SubscriptionError error;
}

/// Subscription Service Interface - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Uses Go Backend API ONLY for subscriptions
/// NO direct Supabase calls
/// Clean async/await patterns with retry logic
abstract class SubscriptionService {
  /// Submit a new subscription request using core models
  Future<SubscriptionResult<SubscriptionResponse>> submitSubscriptionRequest(
    SubscriptionRequest request,
  );

  /// Get all subscriptions for a specific user using core models
  Future<SubscriptionResult<List<SubscriptionResponse>>> getUserSubscriptions(
    String userId,
  );

  /// Get the status of a specific subscription using core models
  Future<SubscriptionResult<SubscriptionResponse>> getSubscriptionStatus(
    String subscriptionId,
  );

  /// Update an existing subscription using core models
  Future<SubscriptionResult<SubscriptionResponse>> updateSubscription(
    String subscriptionId,
    SubscriptionRequest request,
  );

  /// Cancel a subscription using core models
  Future<SubscriptionResult<SubscriptionResponse>> cancelSubscription(
    String subscriptionId,
  );

  /// Get current user's subscription (legacy compatibility)
  Future<SubscriptionResult<SellerSubscription?>> getCurrentSubscription();

  /// Get available subscription plans (legacy compatibility)
  Future<SubscriptionResult<List<SubscriptionPlan>>> getAvailablePlans();

  /// Get subscription billing history (legacy compatibility)
  Future<SubscriptionResult<List<SubscriptionBilling>>> getBillingHistory();

  /// Get monthly quota usage (legacy compatibility)
  Future<SubscriptionResult<MonthlyQuotaUsage?>> getMonthlyQuotaUsage();

  /// Get subscription usage statistics (legacy compatibility)
  Future<SubscriptionResult<Map<String, dynamic>>> getSubscriptionUsage();
}

/// Concrete implementation of SubscriptionService using core models exclusively
/// Uses SimpleApiClient for all backend communication
/// Implements retry logic with exponential backoff
/// Provides comprehensive logging for all operations
@Riverpod(keepAlive: true)
class SubscriptionServiceImpl extends _$SubscriptionServiceImpl
    implements SubscriptionService {
  static final _logger = Logger('SubscriptionServiceImpl');

  @override
  SubscriptionService build() {
    return this;
  }

  /// Get the authenticated API client
  SimpleApiClient get _apiClient => ref.read(simpleApiClientProvider);

  /// Get retry service for handling transient failures
  RetryService get _retryService => ref.read(retryServiceProvider);

  // API endpoints
  static const String _subscriptionsEndpoint = '/api/subscriptions';
  static const String _userSubscriptionsEndpoint = '/api/subscriptions/user';

  // Retry configuration
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(seconds: 1);
  static const double _backoffMultiplier = 2.0;

  @override
  Future<SubscriptionResult<SubscriptionResponse>> submitSubscriptionRequest(
    SubscriptionRequest request,
  ) async {
    _logger.info(
      '📝 Submitting subscription request for user: ${request.userId}',
    );

    // Validate request before sending
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      _logger.warning(
        '❌ Subscription request validation failed: $validationErrors',
      );
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'بيانات الاشتراك غير صحيحة',
          fieldErrors: validationErrors,
          code: 'VALIDATION_ERROR',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _submitSubscriptionRequestInternal(request),
      operationName: 'submitSubscriptionRequest',
      context: 'user: ${request.userId}, store: ${request.storeName}',
    );
  }

  @override
  Future<SubscriptionResult<List<SubscriptionResponse>>> getUserSubscriptions(
    String userId,
  ) async {
    _logger.info('📋 Fetching subscriptions for user: $userId');

    if (userId.trim().isEmpty) {
      _logger.warning('❌ Invalid user ID provided');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'معرف المستخدم مطلوب',
          fieldErrors: {'userId': 'معرف المستخدم لا يمكن أن يكون فارغاً'},
          code: 'INVALID_USER_ID',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _getUserSubscriptionsInternal(userId),
      operationName: 'getUserSubscriptions',
      context: 'user: $userId',
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> getSubscriptionStatus(
    String subscriptionId,
  ) async {
    _logger.info('🔍 Fetching subscription status: $subscriptionId');

    if (subscriptionId.trim().isEmpty) {
      _logger.warning('❌ Invalid subscription ID provided');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'معرف الاشتراك مطلوب',
          fieldErrors: {
            'subscriptionId': 'معرف الاشتراك لا يمكن أن يكون فارغاً',
          },
          code: 'INVALID_SUBSCRIPTION_ID',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _getSubscriptionStatusInternal(subscriptionId),
      operationName: 'getSubscriptionStatus',
      context: 'subscription: $subscriptionId',
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> updateSubscription(
    String subscriptionId,
    SubscriptionRequest request,
  ) async {
    _logger.info('✏️ Updating subscription: $subscriptionId');

    if (subscriptionId.trim().isEmpty) {
      _logger.warning('❌ Invalid subscription ID provided');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'معرف الاشتراك مطلوب',
          fieldErrors: {
            'subscriptionId': 'معرف الاشتراك لا يمكن أن يكون فارغاً',
          },
          code: 'INVALID_SUBSCRIPTION_ID',
        ),
      );
    }

    // Validate request before sending
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      _logger.warning(
        '❌ Subscription update validation failed: $validationErrors',
      );
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'بيانات تحديث الاشتراك غير صحيحة',
          fieldErrors: validationErrors,
          code: 'VALIDATION_ERROR',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _updateSubscriptionInternal(subscriptionId, request),
      operationName: 'updateSubscription',
      context: 'subscription: $subscriptionId, user: ${request.userId}',
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> cancelSubscription(
    String subscriptionId,
  ) async {
    _logger.info('❌ Cancelling subscription: $subscriptionId');

    if (subscriptionId.trim().isEmpty) {
      _logger.warning('❌ Invalid subscription ID provided');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'معرف الاشتراك مطلوب',
          fieldErrors: {
            'subscriptionId': 'معرف الاشتراك لا يمكن أن يكون فارغاً',
          },
          code: 'INVALID_SUBSCRIPTION_ID',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _cancelSubscriptionInternal(subscriptionId),
      operationName: 'cancelSubscription',
      context: 'subscription: $subscriptionId',
    );
  }

  /// Internal method to submit subscription request
  Future<SubscriptionResult<SubscriptionResponse>>
  _submitSubscriptionRequestInternal(SubscriptionRequest request) async {
    try {
      _logger.fine('🔄 Making API call to submit subscription request');

      final response = await _apiClient.post<Map<String, dynamic>>(
        _subscriptionsEndpoint,
        data: request.toJson(),
      );

      return _handleApiResponse<SubscriptionResponse>(
        response,
        (data) => SubscriptionResponse.fromJson(data),
        'submitSubscriptionRequest',
      );
    } catch (e, stackTrace) {
      _logger.severe(
        '💥 Exception in submitSubscriptionRequest',
        e,
        stackTrace,
      );
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'submitSubscriptionRequest'),
      );
    }
  }

  /// Internal method to get user subscriptions
  Future<SubscriptionResult<List<SubscriptionResponse>>>
  _getUserSubscriptionsInternal(String userId) async {
    try {
      _logger.fine('🔄 Making API call to get user subscriptions');

      final response = await _apiClient.get<List<dynamic>>(
        '$_userSubscriptionsEndpoint/$userId',
      );

      return _handleApiResponse<List<SubscriptionResponse>>(
        response,
        (data) => (data as List<dynamic>)
            .map(
              (item) =>
                  SubscriptionResponse.fromJson(item as Map<String, dynamic>),
            )
            .toList(),
        'getUserSubscriptions',
      );
    } catch (e, stackTrace) {
      _logger.severe('💥 Exception in getUserSubscriptions', e, stackTrace);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getUserSubscriptions'),
      );
    }
  }

  /// Internal method to get subscription status
  Future<SubscriptionResult<SubscriptionResponse>>
  _getSubscriptionStatusInternal(String subscriptionId) async {
    try {
      _logger.fine('🔄 Making API call to get subscription status');

      final response = await _apiClient.get<Map<String, dynamic>>(
        '$_subscriptionsEndpoint/$subscriptionId',
      );

      return _handleApiResponse<SubscriptionResponse>(
        response,
        (data) => SubscriptionResponse.fromJson(data),
        'getSubscriptionStatus',
      );
    } catch (e, stackTrace) {
      _logger.severe('💥 Exception in getSubscriptionStatus', e, stackTrace);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getSubscriptionStatus'),
      );
    }
  }

  /// Internal method to update subscription
  Future<SubscriptionResult<SubscriptionResponse>> _updateSubscriptionInternal(
    String subscriptionId,
    SubscriptionRequest request,
  ) async {
    try {
      _logger.fine('🔄 Making API call to update subscription');

      final response = await _apiClient.put<Map<String, dynamic>>(
        '$_subscriptionsEndpoint/$subscriptionId',
        data: request.toJson(),
      );

      return _handleApiResponse<SubscriptionResponse>(
        response,
        (data) => SubscriptionResponse.fromJson(data),
        'updateSubscription',
      );
    } catch (e, stackTrace) {
      _logger.severe('💥 Exception in updateSubscription', e, stackTrace);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'updateSubscription'),
      );
    }
  }

  /// Internal method to cancel subscription
  Future<SubscriptionResult<SubscriptionResponse>> _cancelSubscriptionInternal(
    String subscriptionId,
  ) async {
    try {
      _logger.fine('🔄 Making API call to cancel subscription');

      final response = await _apiClient.delete<Map<String, dynamic>>(
        '$_subscriptionsEndpoint/$subscriptionId',
      );

      return _handleApiResponse<SubscriptionResponse>(
        response,
        (data) => SubscriptionResponse.fromJson(data),
        'cancelSubscription',
      );
    } catch (e, stackTrace) {
      _logger.severe('💥 Exception in cancelSubscription', e, stackTrace);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'cancelSubscription'),
      );
    }
  }

  /// Execute operation with exponential backoff retry logic
  Future<SubscriptionResult<T>> _executeWithRetry<T>({
    required Future<SubscriptionResult<T>> Function() operation,
    required String operationName,
    required String context,
  }) async {
    int attempt = 0;
    Duration delay = _baseDelay;

    while (attempt < _maxRetries) {
      attempt++;

      _logger.fine(
        '🔄 Executing $operationName (attempt $attempt/$_maxRetries) - $context',
      );

      try {
        final result = await operation();

        // If successful, return immediately
        if (result is SubscriptionSuccess<T>) {
          _logger.info(
            '✅ $operationName succeeded on attempt $attempt - $context',
          );
          return result;
        }

        // If failed, check if retryable
        final error = (result as SubscriptionFailure<T>).error;
        if (!error.isRetryable || attempt >= _maxRetries) {
          _logger.warning(
            '❌ $operationName failed (non-retryable or max retries reached) - $context: ${error.technicalMessage}',
          );
          return result;
        }

        // Log retry attempt
        _logger.warning(
          '⚠️ $operationName failed (attempt $attempt/$_maxRetries), retrying in ${delay.inMilliseconds}ms - $context: ${error.technicalMessage}',
        );

        // Wait before retry with exponential backoff
        await Future.delayed(delay);
        delay = Duration(
          milliseconds: (delay.inMilliseconds * _backoffMultiplier).round(),
        );
      } catch (e, stackTrace) {
        _logger.severe(
          '💥 Unexpected exception in $operationName (attempt $attempt/$_maxRetries) - $context',
          e,
          stackTrace,
        );

        // If this is the last attempt, return the error
        if (attempt >= _maxRetries) {
          return SubscriptionResult.failure(
            _createErrorFromException(e, operationName),
          );
        }

        // Wait before retry
        await Future.delayed(delay);
        delay = Duration(
          milliseconds: (delay.inMilliseconds * _backoffMultiplier).round(),
        );
      }
    }

    // This should never be reached, but just in case
    _logger.severe('💥 $operationName exhausted all retries - $context');
    return SubscriptionResult.failure(
      SubscriptionError.unknownError(
        message: 'تم استنفاد جميع محاولات إعادة المحاولة',
        code: 'MAX_RETRIES_EXCEEDED',
        details: {'operation': operationName, 'context': context},
      ),
    );
  }

  /// Handle API response and convert to SubscriptionResult
  SubscriptionResult<T> _handleApiResponse<T>(
    dynamic response,
    T Function(dynamic data) dataMapper,
    String operationName,
  ) {
    try {
      // Check if response has isSuccess property (SimpleApiClient response)
      if (response is Map && response.containsKey('isSuccess')) {
        if (response['isSuccess'] == true && response['data'] != null) {
          final mappedData = dataMapper(response['data']);
          _logger.fine('✅ $operationName API response processed successfully');
          return SubscriptionResult.success(mappedData);
        } else if (response['isSuccess'] == false) {
          _logger.warning(
            '❌ $operationName API returned error: ${response['error']}',
          );
          return SubscriptionResult.failure(
            _createErrorFromApiResponse(
              Map<String, dynamic>.from(response),
              operationName,
            ),
          );
        }
      }

      // Direct response handling
      if (response != null) {
        try {
          final mappedData = dataMapper(response);
          _logger.fine('✅ $operationName API response processed successfully');
          return SubscriptionResult.success(mappedData);
        } catch (e, stackTrace) {
          _logger.severe(
            '💥 Failed to map API response data in $operationName',
            e,
            stackTrace,
          );
          return SubscriptionResult.failure(
            SubscriptionError.serverError(
              message: 'فشل في معالجة استجابة الخادم',
              code: 'RESPONSE_MAPPING_ERROR',
              details: {'originalError': e.toString()},
            ),
          );
        }
      }

      _logger.warning(
        '❌ $operationName API returned unexpected response state',
      );
      return SubscriptionResult.failure(
        SubscriptionError.serverError(
          message: 'استجابة غير متوقعة من الخادم',
          code: 'UNEXPECTED_RESPONSE',
          details: {'response': response.toString()},
        ),
      );
    } catch (e, stackTrace) {
      _logger.severe(
        '💥 Exception in _handleApiResponse for $operationName',
        e,
        stackTrace,
      );
      return SubscriptionResult.failure(
        _createErrorFromException(e, operationName),
      );
    }
  }

  /// Create SubscriptionError from API response
  SubscriptionError _createErrorFromApiResponse(
    Map<String, dynamic> response,
    String operationName,
  ) {
    final errorMessage =
        response['error']?.toString() ?? 'خطأ غير معروف من الخادم';

    // Try to parse error message for specific error types
    if (errorMessage.toLowerCase().contains('database') ||
        errorMessage.toLowerCase().contains('db')) {
      return SubscriptionError.databaseError(
        message: 'خطأ في قاعدة البيانات',
        code: 'DATABASE_ERROR',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    if (errorMessage.toLowerCase().contains('network') ||
        errorMessage.toLowerCase().contains('connection') ||
        errorMessage.toLowerCase().contains('timeout')) {
      return SubscriptionError.networkError(
        message: 'خطأ في الاتصال بالخادم',
        code: 'NETWORK_ERROR',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    if (errorMessage.toLowerCase().contains('unauthorized') ||
        errorMessage.toLowerCase().contains('authentication') ||
        errorMessage.toLowerCase().contains('auth')) {
      return SubscriptionError.authenticationError(
        message: 'خطأ في المصادقة',
        code: 'AUTH_ERROR',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    if (errorMessage.toLowerCase().contains('validation') ||
        errorMessage.toLowerCase().contains('invalid')) {
      return SubscriptionError.validationError(
        message: 'بيانات غير صحيحة',
        fieldErrors: {'general': errorMessage},
        code: 'VALIDATION_ERROR',
      );
    }

    // Default to server error
    return SubscriptionError.serverError(
      message: 'خطأ في الخادم',
      code: 'SERVER_ERROR',
      details: {'originalError': errorMessage, 'operation': operationName},
    );
  }

  /// Create SubscriptionError from exception
  SubscriptionError _createErrorFromException(
    dynamic exception,
    String operationName,
  ) {
    final errorMessage = exception.toString();

    // Network-related exceptions
    if (errorMessage.contains('SocketException') ||
        errorMessage.contains('NetworkException') ||
        errorMessage.contains('HttpException') ||
        errorMessage.contains('TimeoutException') ||
        errorMessage.contains('Network socket exception')) {
      return SubscriptionError.networkError(
        message: 'خطأ في الاتصال بالشبكة',
        code: 'NETWORK_EXCEPTION',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    // Format/parsing exceptions
    if (errorMessage.contains('FormatException') ||
        errorMessage.contains('JsonException') ||
        errorMessage.contains('TypeError')) {
      return SubscriptionError.serverError(
        message: 'خطأ في تنسيق البيانات',
        code: 'DATA_FORMAT_ERROR',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    // Authentication exceptions
    if (errorMessage.contains('AuthException') ||
        errorMessage.contains('Unauthorized')) {
      return SubscriptionError.authenticationError(
        message: 'خطأ في المصادقة',
        code: 'AUTH_EXCEPTION',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    // Default to unknown error
    return SubscriptionError.unknownError(
      message: 'حدث خطأ غير متوقع',
      code: 'UNKNOWN_EXCEPTION',
      details: {'originalError': errorMessage, 'operation': operationName},
    );
  }

  // Legacy compatibility methods - these maintain backward compatibility
  // while internally using core models where possible

  @override
  Future<SubscriptionResult<SellerSubscription?>>
  getCurrentSubscription() async {
    final user = ref.read(currentUserProvider);
    if (user == null) {
      return SubscriptionResult.failure(
        SubscriptionError.authenticationError(
          message: 'المستخدم غير مسجل',
          code: 'USER_NOT_AUTHENTICATED',
        ),
      );
    }

    try {
      _logger.info('Fetching current subscription for legacy compatibility');

      final response = await _apiClient.get<Map<String, dynamic>>(
        '/seller/subscription/current',
      );

      if (response.isSuccess && response.data != null) {
        final subscription = SellerSubscription.fromJson(response.data!);
        return SubscriptionResult.success(subscription);
      } else {
        return SubscriptionResult.failure(
          SubscriptionError.serverError(
            message: 'فشل في جلب الاشتراك الحالي',
            code: 'GET_SUBSCRIPTION_ERROR',
            details: {'error': response.error},
          ),
        );
      }
    } catch (e, st) {
      _logger.severe('Error fetching current subscription', e, st);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getCurrentSubscription'),
      );
    }
  }

  @override
  Future<SubscriptionResult<List<SubscriptionPlan>>> getAvailablePlans() async {
    try {
      _logger.info(
        'Fetching available subscription plans for legacy compatibility',
      );

      final response = await _apiClient.get<List<dynamic>>(
        '/api/v1/seller/subscription/plans',
      );

      if (response.isSuccess && response.data != null) {
        final plans = (response.data! as List)
            .map((json) => SubscriptionPlan.fromJson(json))
            .toList();
        return SubscriptionResult.success(plans);
      } else {
        return SubscriptionResult.failure(
          SubscriptionError.serverError(
            message: 'فشل في جلب خطط الاشتراك',
            code: 'GET_PLANS_ERROR',
            details: {'error': response.error},
          ),
        );
      }
    } catch (e, st) {
      _logger.severe('Error fetching available plans', e, st);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getAvailablePlans'),
      );
    }
  }

  @override
  Future<SubscriptionResult<List<SubscriptionBilling>>>
  getBillingHistory() async {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      return SubscriptionResult.failure(
        SubscriptionError.authenticationError(
          message: 'المستخدم غير مسجل',
          code: 'USER_NOT_AUTHENTICATED',
        ),
      );
    }

    try {
      _logger.info(
        'Fetching subscription billing history for legacy compatibility',
      );

      final response = await _apiClient.get<List<dynamic>>(
        '/api/v1/seller/subscription/billing-history',
      );

      if (response.isSuccess && response.data != null) {
        final billingHistory = (response.data! as List)
            .map((json) => SubscriptionBilling.fromJson(json))
            .toList();
        return SubscriptionResult.success(billingHistory);
      } else {
        return SubscriptionResult.failure(
          SubscriptionError.serverError(
            message: 'فشل في جلب سجل الفوترة',
            code: 'GET_BILLING_ERROR',
            details: {'error': response.error},
          ),
        );
      }
    } catch (e, st) {
      _logger.severe('Error fetching billing history', e, st);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getBillingHistory'),
      );
    }
  }

  @override
  Future<SubscriptionResult<MonthlyQuotaUsage?>> getMonthlyQuotaUsage() async {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      return SubscriptionResult.failure(
        SubscriptionError.authenticationError(
          message: 'المستخدم غير مسجل',
          code: 'USER_NOT_AUTHENTICATED',
        ),
      );
    }

    try {
      _logger.info('Fetching monthly quota usage for legacy compatibility');

      final response = await _apiClient.get<Map<String, dynamic>>(
        '/api/v1/seller/subscription/quota-usage',
      );

      if (response.isSuccess) {
        if (response.data == null) {
          return const SubscriptionResult.success(null);
        }
        final quotaUsage = MonthlyQuotaUsage.fromJson(response.data!);
        return SubscriptionResult.success(quotaUsage);
      } else {
        return SubscriptionResult.failure(
          SubscriptionError.serverError(
            message: 'فشل في جلب استخدام الحصص',
            code: 'GET_QUOTA_ERROR',
            details: {'error': response.error},
          ),
        );
      }
    } catch (e, st) {
      _logger.severe('Error fetching monthly quota usage', e, st);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getMonthlyQuotaUsage'),
      );
    }
  }

  @override
  Future<SubscriptionResult<Map<String, dynamic>>>
  getSubscriptionUsage() async {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    if (!isAuthenticated) {
      return SubscriptionResult.failure(
        SubscriptionError.authenticationError(
          message: 'المستخدم غير مسجل',
          code: 'USER_NOT_AUTHENTICATED',
        ),
      );
    }

    try {
      _logger.info(
        'Fetching subscription usage statistics for legacy compatibility',
      );

      final response = await _apiClient.get<Map<String, dynamic>>(
        '/api/v1/seller/subscription/usage',
      );

      if (response.isSuccess && response.data != null) {
        return SubscriptionResult.success(response.data!);
      } else {
        return SubscriptionResult.failure(
          SubscriptionError.serverError(
            message: 'فشل في جلب إحصائيات الاستخدام',
            code: 'GET_USAGE_ERROR',
            details: {'error': response.error},
          ),
        );
      }
    } catch (e, st) {
      _logger.severe('Error fetching subscription usage', e, st);
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getSubscriptionUsage'),
      );
    }
  }
}

/// Provider for SubscriptionService
@riverpod
SubscriptionService subscriptionService(SubscriptionServiceRef ref) {
  return SubscriptionServiceImpl();
}
