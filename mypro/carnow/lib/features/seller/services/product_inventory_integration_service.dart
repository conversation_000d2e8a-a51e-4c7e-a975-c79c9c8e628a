import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/utils/unified_logger.dart';
import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/networking/simple_api_client.dart';
import 'inventory_api_service.dart';
import '../models/seller_enums.dart';

part 'product_inventory_integration_service.g.dart';

/// خدمة تكامل المنتجات والمخزون
class ProductInventoryIntegrationService {
  ProductInventoryIntegrationService(this._inventoryApiService, this._ref);

  final InventoryApiService _inventoryApiService;
  final Ref _ref;

  /// Get SimpleApiClient for API calls
  SimpleApiClient get _client => _ref.read(simpleApiClientProvider);

  String get _currentUserId {
    final currentUser = _ref.read(currentUserProvider);
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }
    return currentUser.id;
  }

  /// ربط منتج جديد بالمخزون تلقائياً
  Future<void> linkProductToInventory({
    required String productId,
    required String productName,
    int initialStock = 0,
    int minStockLevel = 5,
    int reorderPoint = 10,
    double? costPrice,
    double? sellingPrice,
  }) async {
    try {
      UnifiedLogger.info('Linking product to inventory: $productId');

      await _inventoryApiService.linkProductToInventory(
        sellerId: _currentUserId,
        productId: productId,
        productName: productName,
        initialStock: initialStock,
        minStockLevel: minStockLevel,
        reorderPoint: reorderPoint,
        costPrice: costPrice ?? 0.0,
        sellingPrice: sellingPrice ?? 0.0,
      );

      UnifiedLogger.info(
        'Product successfully linked to inventory: $productId',
      );
    } catch (e) {
      UnifiedLogger.error('Failed to link product to inventory: $e');
      throw Exception('Failed to link product to inventory: $e');
    }
  }

  /// تحديث المخزون عند إتمام عملية بيع
  Future<void> processSale({
    required String productId,
    required int quantitySold,
    required String orderId,
    String? notes,
  }) async {
    try {
      UnifiedLogger.info(
        'Processing sale for product: $productId, quantity: $quantitySold',
      );

      await _inventoryApiService.processSale(
        sellerId: _currentUserId,
        productId: productId,
        quantitySold: quantitySold,
        orderId: orderId,
        notes: notes,
      );

      UnifiedLogger.info('Sale processed successfully for product: $productId');
    } catch (e) {
      UnifiedLogger.error('Failed to process sale: $e');
      throw Exception('Failed to process sale: $e');
    }
  }

  /// إلغاء بيع وإرجاع المخزون
  Future<void> cancelSale({
    required String productId,
    required int quantityToReturn,
    required String orderId,
    String? reason,
  }) async {
    try {
      UnifiedLogger.info(
        'Cancelling sale for product: $productId, quantity: $quantityToReturn',
      );

      // جلب سجل المخزون باستخدام SimpleApiClient
      final response = await _client.getApi<Map<String, dynamic>>(
        '/inventory/product/$productId',
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to fetch inventory data');
      }

      final inventoryData = response.data!;
      final inventoryId = inventoryData['id'];
      final currentStock = inventoryData['current_stock'] as int;
      final availableStock = inventoryData['available_stock'] as int;

      // إعادة المخزون
      final newCurrentStock = currentStock + quantityToReturn;
      final newAvailableStock = availableStock + quantityToReturn;

      await _client.putApi<void>(
        '/inventory/$inventoryId',
        data: {
          'current_stock': newCurrentStock,
          'available_stock': newAvailableStock,
          'last_movement_date': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      // تسجيل حركة الإرجاع
      await _recordStockMovement(
        inventoryId: inventoryId,
        movementType: StockMovementType.returnStock,
        quantity: quantityToReturn,
        orderId: orderId,
        notes: reason ?? 'Sale cancellation',
      );

      // تحديث حالة المنتج
      await _updateProductAvailability(productId, newCurrentStock);

      UnifiedLogger.info('Sale cancelled successfully for product: $productId');
    } catch (e) {
      UnifiedLogger.error('Failed to cancel sale: $e');
      throw Exception('Failed to cancel sale: $e');
    }
  }

  /// حجز مخزون للطلبات المعلقة
  Future<void> reserveStock({
    required String productId,
    required int quantityToReserve,
    required String orderId,
    String? notes,
  }) async {
    try {
      UnifiedLogger.info(
        'Reserving stock for product: $productId, quantity: $quantityToReserve',
      );

      final response = await _client.getApi<Map<String, dynamic>>(
        '/inventory/product/$productId',
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to fetch inventory data');
      }

      final inventoryData = response.data!;
      final inventoryId = inventoryData['id'];
      final availableStock = inventoryData['available_stock'] as int;
      final reservedStock = inventoryData['reserved_stock'] as int;

      if (availableStock < quantityToReserve) {
        throw Exception('Insufficient available stock for reservation');
      }

      // تحديث المخزون المحجوز
      final newAvailableStock = availableStock - quantityToReserve;
      final newReservedStock = reservedStock + quantityToReserve;

      await _client.putApi<void>(
        '/inventory/$inventoryId',
        data: {
          'available_stock': newAvailableStock,
          'reserved_stock': newReservedStock,
          'last_movement_date': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      // تسجيل حركة الحجز
      await _recordStockMovement(
        inventoryId: inventoryId,
        movementType: StockMovementType.reservation,
        quantity: quantityToReserve,
        orderId: orderId,
        notes: notes ?? 'Stock reservation',
      );

      UnifiedLogger.info('Stock reserved successfully for product: $productId');
    } catch (e) {
      UnifiedLogger.error('Failed to reserve stock: $e');
      throw Exception('Failed to reserve stock: $e');
    }
  }

  /// إلغاء حجز المخزون
  Future<void> releaseReservation({
    required String productId,
    required int quantityToRelease,
    required String orderId,
    String? reason,
  }) async {
    try {
      UnifiedLogger.info(
        'Releasing reservation for product: $productId, quantity: $quantityToRelease',
      );

      final response = await _client.getApi<Map<String, dynamic>>(
        '/inventory/product/$productId',
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to fetch inventory data');
      }

      final inventoryData = response.data!;
      final inventoryId = inventoryData['id'];
      final availableStock = inventoryData['available_stock'] as int;
      final reservedStock = inventoryData['reserved_stock'] as int;

      if (reservedStock < quantityToRelease) {
        throw Exception('Cannot release more than reserved quantity');
      }

      // إلغاء الحجز
      final newAvailableStock = availableStock + quantityToRelease;
      final newReservedStock = reservedStock - quantityToRelease;

      await _client.putApi<void>(
        '/inventory/$inventoryId',
        data: {
          'available_stock': newAvailableStock,
          'reserved_stock': newReservedStock,
          'last_movement_date': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      UnifiedLogger.info(
        'Reservation released successfully for product: $productId',
      );
    } catch (e) {
      UnifiedLogger.error('Failed to release reservation: $e');
      throw Exception('Failed to release reservation: $e');
    }
  }

  /// تحديث توفر المنتج بناءً على المخزون
  Future<void> _updateProductAvailability(
    String productId,
    int currentStock,
  ) async {
    try {
      String stockStatus;
      bool isAvailable;

      if (currentStock <= 0) {
        stockStatus = 'out_of_stock';
        isAvailable = false;
      } else if (currentStock <= 5) {
        stockStatus = 'low_stock';
        isAvailable = true;
      } else {
        stockStatus = 'in_stock';
        isAvailable = true;
      }

      await _client.putApi<void>(
        '/products/$productId',
        data: {
          'stock_status': stockStatus,
          'is_available': isAvailable,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      UnifiedLogger.info('Product availability updated: $productId');
    } catch (e) {
      UnifiedLogger.warning('Failed to update inventory status: $e');
    }
  }

  /// تسجيل حركة المخزون
  Future<void> _recordStockMovement({
    required String inventoryId,
    required StockMovementType movementType,
    required int quantity,
    String? orderId,
    String? notes,
  }) async {
    try {
      final movementData = {
        'inventory_id': inventoryId,
        'movement_type': movementType.name,
        'quantity': quantity,
        'order_id': orderId,
        'notes': notes,
        'created_by': _currentUserId,
        'created_at': DateTime.now().toIso8601String(),
      };

      await _client.postApi<void>('/stock-movements', data: movementData);

      UnifiedLogger.info('Stock movement recorded: ${movementType.name}');
    } catch (e) {
      UnifiedLogger.error('Failed to record stock movement: $e');
    }
  }

  /// الحصول على إحصائيات المخزون للمنتج
  Future<Map<String, dynamic>> getProductInventoryStats(
    String productId,
  ) async {
    try {
      final inventoryResponse = await _client.getApi<Map<String, dynamic>>(
        '/inventory/product/$productId',
      );

      if (!inventoryResponse.isSuccess || inventoryResponse.data == null) {
        throw Exception('Failed to fetch inventory data');
      }

      final inventoryData = inventoryResponse.data!;
      final inventoryId = inventoryData['id'];

      // جلب حركات المخزون الأخيرة
      final movementsResponse = await _client.getApi(
        '/stock-movements/inventory/$inventoryId',
        queryParameters: {'limit': 10, 'order': 'created_at desc'},
      );

      final recentMovements = (movementsResponse.data as List<dynamic>?) ?? [];

      return {'inventory': inventoryData, 'recent_movements': recentMovements};
    } catch (e) {
      UnifiedLogger.error('Failed to get product inventory stats: $e');
      throw Exception('Failed to get product inventory stats: $e');
    }
  }

  /// تحديث السعر في المخزون
  Future<void> updateInventoryPricing({
    required String productId,
    double? costPrice,
    double? sellingPrice,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (costPrice != null) updateData['cost_price'] = costPrice;
      if (sellingPrice != null) updateData['selling_price'] = sellingPrice;

      await _client.putApi<void>(
        '/inventory/product/$productId/pricing',
        data: updateData,
      );

      UnifiedLogger.info('Inventory pricing updated for product: $productId');
    } catch (e) {
      UnifiedLogger.error('Failed to update inventory pricing: $e');
      throw Exception('Failed to update inventory pricing: $e');
    }
  }
}

/// مزود خدمة تكامل المنتجات والمخزون
@riverpod
ProductInventoryIntegrationService productInventoryIntegrationService(Ref ref) {
  final inventoryApiService = ref.read(inventoryApiServiceProvider);
  return ProductInventoryIntegrationService(inventoryApiService, ref);
}
