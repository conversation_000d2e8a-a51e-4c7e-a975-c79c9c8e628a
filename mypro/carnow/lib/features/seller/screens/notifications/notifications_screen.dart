/// شاشة إشعارات البائع
///
/// تعرض قائمة بجميع الإشعارات المتعلقة بأنشطة البائع،
/// مثل استلام طلب جديد، تحديث حالة طلب، أو رسائل من المشترين.
/// تتيح للبائع وضع علامة "مقروء" على الإشعارات أو حذفها.
library;

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

import '../../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/app_error_widget.dart';
import '../../../../shared/widgets/app_loading_indicator.dart';
import '../../models/seller_notification_model.dart';
import '../../providers/seller_notifications_async_provider.dart';

class SellerNotificationsScreen extends HookConsumerWidget {
  const SellerNotificationsScreen({super.key});

  static final _logger = Logger('SellerNotificationsScreen');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsAsync = ref.watch(sellerNotificationsAsyncProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        actions: [
          IconButton(
            onPressed: () => _showClearAllDialog(context, ref),
            icon: const Icon(Icons.notifications_off),
            tooltip: 'وضع علامة قراءة على الكل',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          try {
            // ✅ Using UnifiedAuthProvider instead of direct Supabase auth
            final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
            if (authSystem.isAuthenticated) {
              await ref
                  .read(sellerNotificationsAsyncProvider.notifier)
                  .refresh();
            }
          } catch (e, stack) {
            _logger.severe('Error refreshing notifications', e, stack);
          }
        },
        child: notificationsAsync.when(
          data: (notifications) {
            if (notifications.isEmpty) {
              return const _EmptyNotificationsView();
            }

            return ListView.builder(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return _NotificationItem(
                  key: ValueKey(notification.id),
                  notification: notification,
                );
              },
            );
          },
          loading: () => const Center(child: AppLoadingIndicator()),
          error: (error, stack) {
            _logger.severe('Failed to load notifications', error, stack);
            return AppErrorWidget(
              message: 'فشل في تحميل الإشعارات',
              details: error.toString(),
              onRetry: () {
                // ✅ Using UnifiedAuthProvider instead of direct Supabase
                final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
                if (authSystem.isAuthenticated) {
                  ref.invalidate(sellerNotificationsAsyncProvider);
                }
              },
            );
          },
        ),
      ),
    );
  }

  /// عرض مربع حوار لوضع علامة قراءة على كل الإشعارات
  Future<void> _showClearAllDialog(BuildContext context, WidgetRef ref) async =>
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) => AlertDialog(
          title: const Text('وضع علامة قراءة على الكل'),
          content: const Text('هل تريد وضع علامة "مقروءة" على جميع الإشعارات؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  // ✅ Using UnifiedAuthProvider instead of direct Supabase
                  final authSystem = ref.read(simpleSupabaseAuthProvider.notifier);
                  if (authSystem.isAuthenticated) {
                    await ref
                        .read(sellerNotificationsAsyncProvider.notifier)
                        .markAllAsRead();
                    if (context.mounted) Navigator.of(context).pop();
                  }
                } catch (e, stack) {
                  _logger.severe('Error marking all as read', e, stack);
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('حدث خطأ: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    Navigator.of(context).pop();
                  }
                }
              },
              child: const Text('تأكيد'),
            ),
          ],
        ),
      );
}

/// Widget for empty notifications state
class _EmptyNotificationsView extends StatelessWidget {
  const _EmptyNotificationsView();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off,
            size: 64,
            color: theme.colorScheme.secondary.withAlpha((0.5 * 255).toInt()),
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            'لا توجد إشعارات جديدة',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha((0.7 * 255).toInt()),
            ),
          ),
        ],
      ),
    );
  }
}

class _NotificationItem extends HookConsumerWidget {
  const _NotificationItem({required this.notification, super.key});
  final SellerNotification notification;
  static final _logger = Logger('_NotificationItem');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('dd/MM/yyyy - HH:mm', 'ar');

    // اختيار اللون ورمز الإشعار حسب نوعه
    final (color, icon) = _getNotificationIcon(notification.notificationType);

    return Dismissible(
      key: ValueKey(notification.id),
      background: Container(
        color: theme.colorScheme.primary,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: AppTheme.spacingM),
        child: const Icon(Icons.visibility, color: Colors.white),
      ),
      secondaryBackground: Container(
        color: theme.colorScheme.error,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: AppTheme.spacingM),
        child: const Icon(Icons.delete_outline, color: Colors.white),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          // Swipe from left: Mark as read
          try {
            await ref
                .read(sellerNotificationsAsyncProvider.notifier)
                .markAsRead(notification.id);
            return false;
          } catch (e, stack) {
            _logger.severe('Error marking notification as read', e, stack);
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('حدث خطأ: ${e.toString()}'),
                  backgroundColor: theme.colorScheme.error,
                ),
              );
            }
            return false;
          }
        } else {
          // Swipe from right: Delete
          return await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('حذف الإشعار'),
                  content: const Text('هل أنت متأكد من حذف هذا الإشعار؟'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text('حذف'),
                    ),
                  ],
                ),
              ) ??
              false;
        }
      },
      onDismissed: (direction) async {
        try {
          if (direction == DismissDirection.endToStart) {
            await ref
                .read(sellerNotificationsAsyncProvider.notifier)
                .deleteNotification(notification.id);
          }
        } catch (e, stack) {
          _logger.severe('Error deleting notification', e, stack);
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('حدث خطأ في حذف الإشعار: ${e.toString()}'),
                backgroundColor: theme.colorScheme.error,
              ),
            );
          }
        }
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
        color: notification.isRead
            ? theme.cardColor
            : theme.colorScheme.surfaceContainerHighest,
        elevation: 1,
        child: ListTile(
          contentPadding: const EdgeInsets.all(AppTheme.spacingM),
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withAlpha((0.2 * 255).toInt()),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color),
          ),
          title: Text(
            notification.title,
            style: TextStyle(
              fontWeight: notification.isRead ? null : FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: AppTheme.spacingS),
              Text(notification.message),
              const SizedBox(height: AppTheme.spacingS),
              Text(
                dateFormat.format(notification.createdAt),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(
                    (0.6 * 255).toInt(),
                  ),
                ),
              ),
            ],
          ),
          onTap: () async {
            try {
              await ref
                  .read(sellerNotificationsAsyncProvider.notifier)
                  .markAsRead(notification.id);
              if (context.mounted) {
                _handleNotificationTap(context, notification);
              }
            } catch (e, stack) {
              _logger.severe('Error handling notification tap', e, stack);
            }
          },
          isThreeLine: true,
        ),
      ),
    );
  }

  /// اختيار لون ورمز مناسبين لنوع الإشعار
  (Color, IconData) _getNotificationIcon(String type) {
    switch (type) {
      case 'new_order':
        return (Colors.green, Icons.shopping_cart);
      case 'inventory':
        return (Colors.orange, Icons.inventory);
      case 'payment':
        return (Colors.green.shade700, Icons.payments);
      case 'review':
        return (Colors.amber, Icons.star);
      case 'system':
        return (Colors.blue, Icons.info);
      default:
        return (Colors.blue, Icons.notifications);
    }
  }

  /// معالجة النقر على الإشعار
  void _handleNotificationTap(
    BuildContext context,
    SellerNotification notification,
  ) {
    final relatedId = notification.relatedId;
    if (relatedId == null) return;

    // التعامل مع أنواع مختلفة من الإشعارات والانتقال إلى الشاشات المناسبة
    switch (notification.notificationType) {
      case 'new_order':
        Navigator.of(context).pushNamed(
          '/seller/orders/details',
          arguments: {'orderId': relatedId},
        );
        break;
      case 'inventory':
        Navigator.of(context).pushNamed(
          '/seller/products/details',
          arguments: {'productId': relatedId},
        );
        break;
      case 'payment':
        Navigator.of(
          context,
        ).pushNamed('/seller/payments', arguments: {'paymentId': relatedId});
        break;
      case 'review':
        Navigator.of(
          context,
        ).pushNamed('/seller/reviews', arguments: {'reviewId': relatedId});
        break;
      default:
        // لا يوجد تنقل خاص للإشعارات الأخرى
        break;
    }
  }
}
