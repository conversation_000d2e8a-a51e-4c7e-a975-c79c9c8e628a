import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/simple_supabase_auth_provider.dart';
import '../../../core/providers/users_provider.dart';

part 'dashboard_provider.g.dart';

@riverpod
class DashboardData extends _$DashboardData {
  @override
  Map<String, dynamic> build() {
    final authState = ref.watch(simpleSupabaseAuthProvider);
    final currentUserProfileAsync = ref.watch(currentUserStreamProvider);
    final currentUserProfile = currentUserProfileAsync.value;
    
    // Safe access to user data with null checks
    final userName = currentUserProfile?.name ?? 
        (authState is SimpleAuthStateAuthenticated ? authState.user.email : null) ?? 'مستخدم';
    final userEmail = authState is SimpleAuthStateAuthenticated ? authState.user.email : '';
    final isAuthenticated = authState is SimpleAuthStateAuthenticated;

    return {
      'user_name': userName,
      'user_email': userEmail,
      'is_authenticated': isAuthenticated,
      'greeting': _getGreeting(),
      'quick_actions': _getQuickActions(isAuthenticated),
      'stats': _getStats(),
      'last_updated': DateTime.now().toIso8601String(),
    };
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'صباح الخير';
    } else if (hour < 17) {
      return 'مساء الخير';
    } else {
      return 'مساء الخير';
    }
  }

  List<Map<String, dynamic>> _getQuickActions(bool isAuthenticated) {
    if (!isAuthenticated) {
      return [
        {'title': 'تسجيل الدخول', 'icon': 'login', 'route': '/login'},
        {'title': 'تصفح المنتجات', 'icon': 'products', 'route': '/products'},
      ];
    }

    return [
      {'title': 'طلباتي', 'icon': 'orders', 'route': '/orders'},
      {'title': 'المفضلة', 'icon': 'favorites', 'route': '/favorites'},
      {'title': 'الملف الشخصي', 'icon': 'profile', 'route': '/profile'},
      {'title': 'تصفح المنتجات', 'icon': 'products', 'route': '/products'},
    ];
  }

  Map<String, int> _getStats() {
    // TODO: Implement real stats from database
    return {
      'total_products': 0,
      'total_orders': 0,
      'total_favorites': 0,
    };
  }

  void refresh() {
    ref.invalidateSelf();
  }
}
