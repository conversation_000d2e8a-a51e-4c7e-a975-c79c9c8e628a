import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:dio/dio.dart';

import '../auth/simple_supabase_auth_provider.dart';
import '../config/backend_config.dart';

final _logger = Logger();

/// Simple App Initialization - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// Uses SimpleSupabaseAuthProvider for clean, simple authentication
class SimpleAppInitialization {
  static ProviderContainer? _container;
  
  /// Initialize the app with SimpleSupabaseAuthProvider
  static Future<ProviderContainer> initialize() async {
    try {
      _logger.i('🚀 Starting CarNow app initialization with SimpleSupabaseAuthProvider...');
      
      // Create provider container
      _container = ProviderContainer();
      
      // Initialize simple auth provider
      await _initializeSimpleAuthProvider(_container!);
      
      // Wake up backend services asynchronously
      _wakeUpBackendAsync();
      
      _logger.i('✅ CarNow app initialization completed successfully');
      return _container!;
    } catch (e, stack) {
      _logger.e('❌ App initialization failed', error: e, stackTrace: stack);
      
      // Graceful degradation - create container anyway
      _container ??= ProviderContainer();
      _logger.w('⚠️ Continuing with offline functionality');
      return _container!;
    }
  }

  /// Simple backend wake-up
  static void _wakeUpBackendAsync() {
    Future.microtask(() async {
      try {
        _logger.i('🌐 Detecting best backend server...');

        final bestServer = await BackendConfig.detectBestServer();
        _logger.i('✅ Using backend server: $bestServer');

        final dio = Dio();
        dio.options.connectTimeout = const Duration(seconds: 5);
        dio.options.receiveTimeout = const Duration(seconds: 5);

        await dio.get('$bestServer/health');
        _logger.i('✅ Backend ping completed');
      } catch (e) {
        _logger.w('⚠️ Backend ping failed (normal if sleeping): $e');
      }
    });
  }
  
  /// Initialize simple auth provider
  static Future<void> _initializeSimpleAuthProvider(ProviderContainer container) async {
    try {
      // Initialize the simple auth provider
      final authState = container.read(simpleSupabaseAuthProvider);
      
      _logger.i('✅ Simple auth provider initialized with state: ${authState.runtimeType}');
      
      // Log the initialization phase
      if (authState is SimpleAuthStateInitial) {
        _logger.d('Simple auth provider in initial state');
      } else if (authState is SimpleAuthStateAuthenticated) {
        _logger.i('User session restored successfully');
      } else if (authState is SimpleAuthStateUnauthenticated) {
        _logger.i('No existing session found - user needs to sign in');
      } else if (authState is SimpleAuthStateError) {
        _logger.w('Simple auth provider initialized with error state');
      }
      
    } catch (e) {
      _logger.e('Failed to initialize simple auth provider: $e');
      rethrow;
    }
  }
  
  /// Cleanup resources
  static Future<void> cleanup() async {
    try {
      _logger.i('🧹 Cleaning up app resources...');
      
      if (_container != null) {
        _container!.dispose();
        _container = null;
      }
      
      _logger.i('✅ App cleanup completed');
    } catch (e) {
      _logger.e('❌ App cleanup failed: $e');
    }
  }
  
  /// Get the current container
  static ProviderContainer? get container => _container;
} 