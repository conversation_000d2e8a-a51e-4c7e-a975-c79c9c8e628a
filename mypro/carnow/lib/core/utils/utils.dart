// Central export file for all utility functions
// Import this file to access all utility functions:
// import 'package:carnow/core/utils/utils.dart';

// === CORE UTILITIES - أساسيات التطبيق ===
export 'app_colors.dart';
export 'app_constants.dart';
export 'app_extensions.dart';
export 'app_styles.dart';

// === UNIFIED SYSTEMS - الأنظمة الموحدة ===
// استخدام نظام التسجيل الموحد الجديد
export 'unified_logger.dart';
// أدوات الحوسبة والتحسين الموحدة
export 'compute_utils.dart';
// نظام المصادقة الموحد - Simple Auth System
export '../auth/simple_supabase_auth_provider.dart';
export '../auth/auth_interfaces.dart';
// Note: auth_providers.dart removed to avoid currentUser naming conflict

// === DATA PROCESSING - معالجة البيانات ===
export 'date_formatter.dart';
export 'formatters.dart';
export 'json_converters.dart';
export 'type_helpers.dart';

// === ERROR HANDLING - معالجة الأخطاء ===
export 'error_handler.dart';
export 'exceptions.dart';
export 'result.dart';

// === UI/THEME UTILITIES - أدوات واجهة المستخدم ===
export 'contextual_theme.dart';
export 'color_utils.dart'; // أدوات الألوان الموحدة (تم دمج shared/utils/color_utils.dart)
export 'image_utils.dart';
export 'unified_theme_extension.dart';
// إضافة الملفات من shared/utils
export '../../shared/utils/ui_error_helper.dart';

// === OTHER UTILITIES - أدوات أخرى ===
export 'async_value_extension.dart';
export 'validators.dart';

// === SPECIALIZED TOOLS - أدوات متخصصة ===
// إصلاح مشاكل أداء المصادقة
export 'auth_performance_fix.dart';

// ================================================================================
// === DEPRECATED EXPORTS - ملفات محتقة سيتم إزالتها قريباً ===
// ================================================================================

// هذه الملفات محتقة وسيتم إزالتها - استخدم البدائل الموحدة
// These files are deprecated and will be removed - use unified alternatives

// نظم التسجيل القديمة - استخدم unified_logger.dart
// export 'logger.dart'; // DEPRECATED -> use unified_logger.dart
// export 'logging_utils.dart'; // DEPRECATED -> use unified_logger.dart
// export 'app_logger.dart'; // DEPRECATED -> use unified_logger.dart
// export 'print_replacer.dart'; // DEPRECATED -> use unified_logger.dart

// أدوات التحسين القديمة - استخدم compute_utils.dart
// export 'optimization_utils.dart'; // DEPRECATED -> use compute_utils.dart

// ================================================================================
// === COMMENTED OUT - FUTURE FEATURES ===
// ================================================================================
// هذه الملفات قد يتم إضافتها مستقبلاً حسب الحاجة

// Navigation
// export 'navigation_utils.dart';
// export 'route_utils.dart';

// UI utilities
// export 'ui_utils.dart';
// export 'responsive_utils.dart';

// Validation
// export 'validation_utils.dart';
// export 'formatter_utils.dart';

// File utilities
// export 'file_utils.dart';

// Network utilities
// export 'network_utils.dart';
// export 'api_utils.dart';

// Storage utilities
// export 'storage_utils.dart';
// export 'preferences_utils.dart';

// System utilities
// export 'device_utils.dart';
// export 'platform_utils.dart';
