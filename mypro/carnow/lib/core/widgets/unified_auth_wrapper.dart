import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../auth/simple_supabase_auth_provider.dart';
import '../app/carnow_app.dart';
import '../../features/auth/screens/new_login_screen.dart';

/// AuthWrapper مبسط يستخدم SimpleSupabaseAuthProvider
/// Simplified AuthWrapper using SimpleSupabaseAuthProvider
class UnifiedAuthWrapper extends ConsumerWidget {
  const UnifiedAuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مراقبة حالة المصادقة من SimpleSupabaseAuthProvider
    // Watch auth state from SimpleSupabaseAuthProvider
    final authState = ref.watch(simpleSupabaseAuthProvider);

    // Handle different auth states
    if (authState is SimpleAuthStateLoading) {
      return const Center(child: CircularProgressIndicator());
    } else if (authState is SimpleAuthStateAuthenticated) {
      return const CarNowApp();
    } else if (authState is SimpleAuthStateUnauthenticated) {
      return const NewLoginScreen();
    } else if (authState is SimpleAuthStateError) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                authState.message,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              FilledButton(
                onPressed: () {
                  // إعادة تحميل النظام
                  ref.invalidate(simpleSupabaseAuthProvider);
                },
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    // Default fallback
    return const Center(child: CircularProgressIndicator());
  }
}
