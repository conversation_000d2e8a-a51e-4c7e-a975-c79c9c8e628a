import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../auth/simple_supabase_auth_provider.dart';
import '../models/user_model.dart';
import '../../features/auth/screens/new_login_screen.dart';

/// Widget that depends on authentication state
class AuthDependentWidget extends ConsumerWidget {
  const AuthDependentWidget({
    super.key,
    required this.authenticatedBuilder,
    this.unauthenticatedWidget,
    this.title,
  });

  final Widget Function(BuildContext context, String userId)
      authenticatedBuilder;
  final Widget? unauthenticatedWidget;
  final String? title;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(simpleSupabaseAuthProvider);
    
    return switch (authState) {
      SimpleAuthStateAuthenticated(user: final user) => authenticatedBuilder(context, user.id),
      SimpleAuthStateUnauthenticated() => unauthenticatedWidget ?? _buildDefaultUnauthenticatedWidget(context),
      SimpleAuthStateLoading() => const Center(child: CircularProgressIndicator()),
      SimpleAuthStateError(message: final message) => Center(child: Text('خطأ في المصادقة: $message')),
      SimpleAuthStateInitial() => const Center(child: CircularProgressIndicator()),
      _ => const Center(child: CircularProgressIndicator()),
    };
  }

  Widget _buildDefaultUnauthenticatedWidget(BuildContext context) => Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.lock_outline, size: 64, color: Colors.grey.shade400),
        const SizedBox(height: 16),
        Text(
          title ?? 'يتطلب تسجيل الدخول',
          style: Theme.of(context).textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'يرجى تسجيل الدخول للوصول إلى هذه الميزة',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        FilledButton(
          onPressed: () {
            Navigator.of(context).push<void>(
              MaterialPageRoute<void>(
                builder: (context) => const NewLoginScreen(),
              ),
            );
          },
          child: const Text('تسجيل الدخول'),
        ),
      ],
    ),
  );
}

/// Widget that shows user profile information when authenticated
class UserProfileWidget extends ConsumerWidget {
  const UserProfileWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(simpleSupabaseAuthProvider);
    
    return switch (authState) {
      SimpleAuthStateAuthenticated(user: final user) => _buildUserProfile(context, user),
      SimpleAuthStateUnauthenticated() => _buildDefaultUnauthenticatedWidget(context),
      SimpleAuthStateLoading() => const Center(child: CircularProgressIndicator()),
      SimpleAuthStateError(message: final message) => Center(child: Text('خطأ في المصادقة: $message')),
      SimpleAuthStateInitial() => const Center(child: CircularProgressIndicator()),
      _ => const Center(child: CircularProgressIndicator()),
    };
  }

  Widget _buildUserProfile(BuildContext context, UserModel user) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  child: Text(
                    user.name?.substring(0, 1).toUpperCase() ?? 'U',
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name ?? 'مستخدم',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      if (user.email != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          user.email!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultUnauthenticatedWidget(BuildContext context) => Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.person_outline, size: 64, color: Colors.grey.shade400),
        const SizedBox(height: 16),
        Text(
          'الملف الشخصي',
          style: Theme.of(context).textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'يرجى تسجيل الدخول لعرض الملف الشخصي',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        FilledButton(
          onPressed: () {
            Navigator.of(context).push<void>(
              MaterialPageRoute<void>(
                builder: (context) => const NewLoginScreen(),
              ),
            );
          },
          child: const Text('تسجيل الدخول'),
        ),
      ],
    ),
  );
}

/// Widget that shows different content based on authentication status
class ConditionalAuthWidget extends ConsumerWidget {
  const ConditionalAuthWidget({
    required this.authenticatedChild,
    required this.unauthenticatedChild,
    super.key,
  });
  final Widget authenticatedChild;
  final Widget unauthenticatedChild;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: isAuthenticated ? authenticatedChild : unauthenticatedChild,
    );
  }
}
