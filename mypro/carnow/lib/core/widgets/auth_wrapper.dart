import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../features/auth/screens/new_login_screen.dart';
import '../auth/simple_supabase_auth_provider.dart';

final _logger = Logger();

/// Widget that wraps the entire app and manages authentication state
/// This ensures consistent auth behavior across all screens
class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({
    required this.child,
    super.key,
    this.requireAuth = false,
    this.fallbackWidget,
  });
  final Widget child;
  final bool requireAuth;
  final Widget? fallbackWidget;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(simpleSupabaseAuthProvider);

    _logger.d('AuthWrapper: Current auth state: $authState');

    // Handle different auth states
    if (authState is SimpleAuthStateLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    } else if (authState is SimpleAuthStateAuthenticated) {
      return child;
    } else if (authState is SimpleAuthStateUnauthenticated) {
      if (requireAuth) {
        return fallbackWidget ?? const NewLoginScreen();
      }
      return child;
    } else if (authState is SimpleAuthStateError) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ في المصادقة',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                authState.message,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              FilledButton(
                onPressed: () {
                  // Refresh auth state
                  ref.invalidate(simpleSupabaseAuthProvider);
                },
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    // Default fallback
    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}

/// Widget that shows different content based on authentication status
class AuthStatusBuilder extends ConsumerWidget {
  const AuthStatusBuilder({
    required this.authenticatedBuilder,
    required this.unauthenticatedBuilder,
    super.key,
    this.loadingBuilder,
    this.errorBuilder,
  });
  final Widget Function(BuildContext context) authenticatedBuilder;
  final Widget Function(BuildContext context) unauthenticatedBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context)? errorBuilder;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(simpleSupabaseAuthProvider);

    // Handle different auth states
    if (authState is SimpleAuthStateLoading) {
      return loadingBuilder?.call(context) ??
          const Center(child: CircularProgressIndicator());
    } else if (authState is SimpleAuthStateAuthenticated) {
      return authenticatedBuilder(context);
    } else if (authState is SimpleAuthStateUnauthenticated) {
      return unauthenticatedBuilder(context);
    } else if (authState is SimpleAuthStateError) {
      return errorBuilder?.call(context) ??
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red),
                const SizedBox(height: 8),
                Text(
                  'خطأ في المصادقة',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          );
    }

    // Default fallback
    return loadingBuilder?.call(context) ??
        const Center(child: CircularProgressIndicator());
  }
}

/// Widget that only shows content when user is authenticated
class AuthenticatedOnly extends ConsumerWidget {
  const AuthenticatedOnly({required this.child, super.key, this.fallback});
  final Widget child;
  final Widget? fallback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    if (isAuthenticated) {
      return child;
    }

    return fallback ?? const SizedBox.shrink();
  }
}

/// Widget that only shows content when user is NOT authenticated
class UnauthenticatedOnly extends ConsumerWidget {
  const UnauthenticatedOnly({required this.child, super.key, this.fallback});
  final Widget child;
  final Widget? fallback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    if (!isAuthenticated) {
      return child;
    }

    return fallback ?? const SizedBox.shrink();
  }
}
