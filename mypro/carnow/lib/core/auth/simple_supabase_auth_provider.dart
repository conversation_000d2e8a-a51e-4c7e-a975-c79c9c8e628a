import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../networking/simple_api_client.dart';
import '../models/user_model.dart';

// =============================================================================
// Auth State Models
// =============================================================================

abstract class SimpleAuthState {
  const SimpleAuthState();
}

class SimpleAuthStateInitial extends SimpleAuthState {
  const SimpleAuthStateInitial();
}

class SimpleAuthStateLoading extends SimpleAuthState {
  const SimpleAuthStateLoading();
}

class SimpleAuthStateAuthenticated extends SimpleAuthState {
  final UserModel user;
  final String token;
  
  const SimpleAuthStateAuthenticated({
    required this.user,
    required this.token,
  });
}

class SimpleAuthStateUnauthenticated extends SimpleAuthState {
  const SimpleAuthStateUnauthenticated();
}

class SimpleAuthStateError extends SimpleAuthState {
  final String message;
  final bool isRecoverable;
  
  const SimpleAuthStateError({
    required this.message,
    this.isRecoverable = false,
  });
}

// =============================================================================
// Auth Request/Response Models
// =============================================================================

class LoginRequest {
  final String email;
  final String password;
  
  const LoginRequest({
    required this.email,
    required this.password,
  });
  
  Map<String, dynamic> toJson() => {
    'email': email,
    'password': password,
  };
}

class LoginResponse {
  final UserModel user;
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  
  const LoginResponse({
    required this.user,
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
  });
  
  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      user: UserModel.fromJson(json['user']),
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      expiresAt: DateTime.parse(json['expires_at']),
    );
  }
}

class RegisterRequest {
  final String email;
  final String password;
  final String name;
  
  const RegisterRequest({
    required this.email,
    required this.password,
    required this.name,
  });
  
  Map<String, dynamic> toJson() => {
    'email': email,
    'password': password,
    'name': name,
  };
}

// =============================================================================
// Simple Supabase Auth Provider
// =============================================================================

/// Simple authentication provider that uses Supabase JWT only
/// Forever Plan Compliant: Flutter UI Only → Go API → Supabase Data
class SimpleSupabaseAuthProvider extends StateNotifier<SimpleAuthState> {
  final SimpleApiClient _apiClient;

  SimpleSupabaseAuthProvider({required SimpleApiClient apiClient})
      : _apiClient = apiClient,
        super(const SimpleAuthStateInitial());

  /// Sign in with email and password
  Future<void> signInWithEmail(String email, String password) async {
    state = const SimpleAuthStateLoading();

    try {
      final response = await _apiClient.post(
        '/auth/login',
        data: LoginRequest(
          email: email,
          password: password,
        ).toJson(),
      );

      final loginResponse = LoginResponse.fromJson(response.data);
      
      state = SimpleAuthStateAuthenticated(
        user: loginResponse.user,
        token: loginResponse.accessToken,
      );

      // Store token for future requests
      await _storeToken(loginResponse.accessToken);
      
    } catch (error) {
      state = SimpleAuthStateError(
        message: _extractErrorMessage(error),
        isRecoverable: _isRecoverableError(error),
      );
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmail(String email, String password, String name) async {
    state = const SimpleAuthStateLoading();

    try {
      final response = await _apiClient.post(
        '/auth/register',
        data: RegisterRequest(
          email: email,
          password: password,
          name: name,
        ).toJson(),
      );

      final loginResponse = LoginResponse.fromJson(response.data);
      
      state = SimpleAuthStateAuthenticated(
        user: loginResponse.user,
        token: loginResponse.accessToken,
      );

      // Store token for future requests
      await _storeToken(loginResponse.accessToken);
      
    } catch (error) {
      state = SimpleAuthStateError(
        message: _extractErrorMessage(error),
        isRecoverable: _isRecoverableError(error),
      );
    }
  }

  /// Sign in with Google OAuth
  Future<void> signInWithGoogle() async {
    state = const SimpleAuthStateLoading();

    try {
      final response = await _apiClient.post(
        '/auth/google',
        data: {'provider': 'google'},
      );

      final loginResponse = LoginResponse.fromJson(response.data);
      
      state = SimpleAuthStateAuthenticated(
        user: loginResponse.user,
        token: loginResponse.accessToken,
      );

      // Store token for future requests
      await _storeToken(loginResponse.accessToken);
      
    } catch (error) {
      state = SimpleAuthStateError(
        message: _extractErrorMessage(error),
        isRecoverable: _isRecoverableError(error),
      );
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      // Call backend to invalidate token
      await _apiClient.post('/auth/logout');
    } catch (error) {
      // Continue with sign out even if backend call fails
      debugPrint('Warning: Failed to invalidate token on backend: $error');
    }

    // Clear local token
    await _clearToken();
    
    // Update state
    state = const SimpleAuthStateUnauthenticated();
  }

  /// Refresh token
  Future<void> refreshToken() async {
    try {
      final currentToken = await _getStoredToken();
      if (currentToken == null) {
        state = const SimpleAuthStateUnauthenticated();
        return;
      }

      final response = await _apiClient.post(
        '/auth/refresh',
        data: {'refresh_token': currentToken},
      );

      final loginResponse = LoginResponse.fromJson(response.data);
      
      state = SimpleAuthStateAuthenticated(
        user: loginResponse.user,
        token: loginResponse.accessToken,
      );

      // Store new token
      await _storeToken(loginResponse.accessToken);
      
    } catch (error) {
      // If refresh fails, sign out
      await signOut();
    }
  }

  /// Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      await _apiClient.post(
        '/auth/change-password',
        data: {'new_password': newPassword},
      );
    } catch (error) {
      throw Exception(_extractErrorMessage(error));
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _apiClient.post(
        '/auth/reset-password',
        data: {'email': email},
      );
    } catch (error) {
      throw Exception(_extractErrorMessage(error));
    }
  }

  /// Resend verification email
  Future<void> resendVerificationEmail(String email) async {
    try {
      await _apiClient.post(
        '/auth/resend-verification',
        data: {'email': email},
      );
    } catch (error) {
      throw Exception(_extractErrorMessage(error));
    }
  }

  /// Check if user is authenticated
  bool get isAuthenticated {
    return state is SimpleAuthStateAuthenticated;
  }

  /// Get current user
  UserModel? get currentUser {
    if (state is SimpleAuthStateAuthenticated) {
      return (state as SimpleAuthStateAuthenticated).user;
    }
    return null;
  }

  /// Get current token
  String? get currentToken {
    if (state is SimpleAuthStateAuthenticated) {
      return (state as SimpleAuthStateAuthenticated).token;
    }
    return null;
  }

  // =============================================================================
  // Private Helper Methods
  // =============================================================================

  /// Store token in secure storage
  Future<void> _storeToken(String token) async {
    // TODO: Implement secure token storage
    // For now, we'll use a simple approach
    // In production, use flutter_secure_storage
  }

  /// Get stored token
  Future<String?> _getStoredToken() async {
    // TODO: Implement secure token retrieval
    return null;
  }

  /// Clear stored token
  Future<void> _clearToken() async {
    // TODO: Implement secure token clearing
  }

  /// Extract error message from exception
  String _extractErrorMessage(dynamic error) {
    if (error is Exception) {
      return error.toString();
    }
    return 'An unexpected error occurred';
  }

  /// Check if error is recoverable
  bool _isRecoverableError(dynamic error) {
    // Network errors and temporary issues are recoverable
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
           errorString.contains('timeout') ||
           errorString.contains('connection');
  }
}

// =============================================================================
// Provider Extensions
// =============================================================================

/// Provider for the simple Supabase auth provider
final simpleSupabaseAuthProvider = StateNotifierProvider<SimpleSupabaseAuthProvider, SimpleAuthState>(
  (ref) => SimpleSupabaseAuthProvider(
    apiClient: ref.read(simpleApiClientProvider),
  ),
);

/// Provider for current user
final currentUserProvider = Provider<UserModel?>((ref) {
  final authState = ref.watch(simpleSupabaseAuthProvider);
  if (authState is SimpleAuthStateAuthenticated) {
    return authState.user;
  }
  return null;
});

/// Provider for authentication status
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(simpleSupabaseAuthProvider);
  return authState is SimpleAuthStateAuthenticated;
}); 