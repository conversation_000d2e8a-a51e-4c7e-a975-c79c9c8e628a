// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'carnow_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$carnowHealthCheckHash() => r'ecbe0af5f667cbcd7a91a8d79de0bf9de52fda9c';

/// Clean CarNow Providers - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// FIXED: Removed all direct Supabase calls
/// ✅ Uses SimpleApiClient ONLY
/// ✅ Uses SimpleAuthSystem for auth
/// ✅ Simple @riverpod patterns only
/// CarNow system health check provider
///
/// Copied from [carnowHealthCheck].
@ProviderFor(carnowHealthCheck)
final carnowHealthCheckProvider = AutoDisposeFutureProvider<bool>.internal(
  carnowHealthCheck,
  name: r'carnowHealthCheckProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$carnowHealthCheckHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CarnowHealthCheckRef = AutoDisposeFutureProviderRef<bool>;
String _$carnowUserNotifierHash() =>
    r'00170085fa4c7180dce889731e7581abd81a93f0';

/// CarNow user notifier provider (alias for currentCarnowUser)
///
/// Copied from [carnowUserNotifier].
@ProviderFor(carnowUserNotifier)
final carnowUserNotifierProvider =
    AutoDisposeFutureProvider<CarnowUser?>.internal(
      carnowUserNotifier,
      name: r'carnowUserNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$carnowUserNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CarnowUserNotifierRef = AutoDisposeFutureProviderRef<CarnowUser?>;
String _$carnowWalletNotifierHash() =>
    r'9fb21f4d4cb6c3e038a701ddf13888ab53154a90';

/// CarNow wallet notifier provider (alias for userWallet)
///
/// Copied from [carnowWalletNotifier].
@ProviderFor(carnowWalletNotifier)
final carnowWalletNotifierProvider =
    AutoDisposeFutureProvider<CarnowWallet?>.internal(
      carnowWalletNotifier,
      name: r'carnowWalletNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$carnowWalletNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CarnowWalletNotifierRef = AutoDisposeFutureProviderRef<CarnowWallet?>;
String _$currentCarnowUserHash() => r'c0dd93d4712af42f87dbffdb1621205c607194e7';

/// Current CarNow user provider using Go backend API ONLY
///
/// Copied from [currentCarnowUser].
@ProviderFor(currentCarnowUser)
final currentCarnowUserProvider =
    AutoDisposeFutureProvider<CarnowUser?>.internal(
      currentCarnowUser,
      name: r'currentCarnowUserProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentCarnowUserHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentCarnowUserRef = AutoDisposeFutureProviderRef<CarnowUser?>;
String _$userWalletHash() => r'38cc61149aa130ae3c113dbc585d7c6c950d4d23';

/// User wallet provider using Go backend API ONLY
///
/// Copied from [userWallet].
@ProviderFor(userWallet)
final userWalletProvider = AutoDisposeFutureProvider<CarnowWallet?>.internal(
  userWallet,
  name: r'userWalletProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userWalletHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserWalletRef = AutoDisposeFutureProviderRef<CarnowWallet?>;
String _$walletTransactionsHash() =>
    r'86b9eed48ca8ef78ba5ecd7e52247cf0f9532450';

/// Wallet transactions provider
///
/// Copied from [walletTransactions].
@ProviderFor(walletTransactions)
final walletTransactionsProvider =
    AutoDisposeFutureProvider<List<CarnowTransaction>>.internal(
      walletTransactions,
      name: r'walletTransactionsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$walletTransactionsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WalletTransactionsRef =
    AutoDisposeFutureProviderRef<List<CarnowTransaction>>;
String _$carnowUserActionsHash() => r'308d46ec83d84f143d5e55e63750c8f2ef58a114';

/// Create or update CarNow user provider
///
/// Copied from [CarnowUserActions].
@ProviderFor(CarnowUserActions)
final carnowUserActionsProvider =
    AutoDisposeAsyncNotifierProvider<CarnowUserActions, CarnowUser?>.internal(
      CarnowUserActions.new,
      name: r'carnowUserActionsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$carnowUserActionsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CarnowUserActions = AutoDisposeAsyncNotifier<CarnowUser?>;
String _$walletActionsHash() => r'7e0f97277da28157acbea5046a0ac30325c26f7c';

/// Wallet actions provider for transactions
///
/// Copied from [WalletActions].
@ProviderFor(WalletActions)
final walletActionsProvider =
    AutoDisposeAsyncNotifierProvider<WalletActions, CarnowWallet?>.internal(
      WalletActions.new,
      name: r'walletActionsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$walletActionsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$WalletActions = AutoDisposeAsyncNotifier<CarnowWallet?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
