import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../models/subscription_request.dart';
import '../models/subscription_response.dart';
import '../models/subscription_error.dart';
import '../services/subscription_service.dart';
import '../auth/simple_supabase_auth_provider.dart';

part 'subscription_flow_provider.g.dart';

final _logger = Logger('CoreSubscriptionFlowProvider');

/// Core Subscription Flow State Management - Forever Plan Architecture
/// Flutter (UI Only) → Core SubscriptionService → Go API → Supabase (Data Only)
///
/// ✅ Uses core SubscriptionRequest/Response models exclusively
/// ✅ Integrates core SubscriptionError types for error state management
/// ✅ Uses core SubscriptionService for all business logic
/// ✅ NO direct Supabase calls
/// ✅ Real data only from Go backend
/// ✅ Material 3 compliant state management
/// ✅ Bilingual error handling (Arabic/English)

// =============================================================================
// CORE FORM STATE MODELS
// =============================================================================

/// Core subscription form data with validation using core models
class CoreSubscriptionFormData {
  final String storeName;
  final String phone;
  final String city;
  final String address;
  final String description;
  final String planType;
  final double price;
  final String? userId;
  final bool isValid;
  final Map<String, String> validationErrors;

  const CoreSubscriptionFormData({
    this.storeName = '',
    this.phone = '',
    this.city = '',
    this.address = '',
    this.description = '',
    this.planType = '',
    this.price = 0.0,
    this.userId,
    this.isValid = false,
    this.validationErrors = const {},
  });

  CoreSubscriptionFormData copyWith({
    String? storeName,
    String? phone,
    String? city,
    String? address,
    String? description,
    String? planType,
    double? price,
    String? userId,
    bool? isValid,
    Map<String, String>? validationErrors,
  }) {
    return CoreSubscriptionFormData(
      storeName: storeName ?? this.storeName,
      phone: phone ?? this.phone,
      city: city ?? this.city,
      address: address ?? this.address,
      description: description ?? this.description,
      planType: planType ?? this.planType,
      price: price ?? this.price,
      userId: userId ?? this.userId,
      isValid: isValid ?? this.isValid,
      validationErrors: validationErrors ?? this.validationErrors,
    );
  }

  /// Convert to core SubscriptionRequest model
  SubscriptionRequest toSubscriptionRequest() {
    if (userId == null || userId!.isEmpty) {
      throw ArgumentError('User ID is required to create subscription request');
    }

    return SubscriptionRequest(
      storeName: storeName,
      phone: phone,
      city: city,
      address: address,
      description: description,
      planType: planType,
      price: price,
      userId: userId!,
    );
  }

  /// Create from core SubscriptionRequest model
  factory CoreSubscriptionFormData.fromSubscriptionRequest(
    SubscriptionRequest request,
  ) {
    return CoreSubscriptionFormData(
      storeName: request.storeName,
      phone: request.phone,
      city: request.city,
      address: request.address,
      description: request.description,
      planType: request.planType,
      price: request.price,
      userId: request.userId,
    );
  }
}

/// Core subscription flow status with real-time updates
enum CoreSubscriptionFlowStatus {
  initial,
  editing,
  validating,
  submitting,
  processing,
  completed,
  failed,
  cancelled,
}

/// Core subscription flow state using core models
class CoreSubscriptionFlowState {
  final CoreSubscriptionFlowStatus status;
  final CoreSubscriptionFormData formData;
  final SubscriptionResponse? response;
  final SubscriptionError? error;
  final double progress;
  final String? successMessage;
  final String? successMessageAr;
  final bool canNavigateBack;
  final bool canNavigateForward;
  final DateTime lastUpdated;

  const CoreSubscriptionFlowState({
    this.status = CoreSubscriptionFlowStatus.initial,
    this.formData = const CoreSubscriptionFormData(),
    this.response,
    this.error,
    this.progress = 0.0,
    this.successMessage,
    this.successMessageAr,
    this.canNavigateBack = false,
    this.canNavigateForward = false,
    required this.lastUpdated,
  });

  CoreSubscriptionFlowState copyWith({
    CoreSubscriptionFlowStatus? status,
    CoreSubscriptionFormData? formData,
    SubscriptionResponse? response,
    SubscriptionError? error,
    double? progress,
    String? successMessage,
    String? successMessageAr,
    bool? canNavigateBack,
    bool? canNavigateForward,
    DateTime? lastUpdated,
  }) {
    return CoreSubscriptionFlowState(
      status: status ?? this.status,
      formData: formData ?? this.formData,
      response: response ?? this.response,
      error: error ?? this.error,
      progress: progress ?? this.progress,
      successMessage: successMessage ?? this.successMessage,
      successMessageAr: successMessageAr ?? this.successMessageAr,
      canNavigateBack: canNavigateBack ?? this.canNavigateBack,
      canNavigateForward: canNavigateForward ?? this.canNavigateForward,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  bool get isLoading =>
      status == CoreSubscriptionFlowStatus.submitting ||
      status == CoreSubscriptionFlowStatus.processing ||
      status == CoreSubscriptionFlowStatus.validating;

  bool get hasError => error != null;

  bool get isCompleted => status == CoreSubscriptionFlowStatus.completed;

  bool get canSubmit => formData.isValid && !isLoading;

  /// Get user-friendly error message in Arabic
  String? get errorMessageArabic => error?.userFriendlyMessageArabic;

  /// Get technical error message for logging
  String? get technicalErrorMessage => error?.technicalMessage;
}

// =============================================================================
// CORE SUBSCRIPTION FLOW PROVIDER
// =============================================================================

/// Main core subscription flow provider with comprehensive state management
@riverpod
class CoreSubscriptionFlowProvider extends _$CoreSubscriptionFlowProvider {
  @override
  CoreSubscriptionFlowState build() {
    return CoreSubscriptionFlowState(lastUpdated: DateTime.now());
  }

  /// Initialize subscription flow with user context
  Future<void> initializeFlow() async {
    try {
      _logger.info('🚀 Initializing core subscription flow');

      final authState = ref.read(simpleSupabaseAuthProvider);
      if (authState is! SimpleAuthStateAuthenticated) {
        throw Exception('User not authenticated');
      }

      final initialFormData = state.formData.copyWith(userId: authState.user.id);

      state = state.copyWith(
        status: CoreSubscriptionFlowStatus.editing,
        formData: initialFormData,
        progress: 0.1,
        canNavigateBack: false,
        canNavigateForward: false,
        error: null,
        lastUpdated: DateTime.now(),
      );

      _logger.info('✅ Core subscription flow initialized successfully');
    } catch (e, stackTrace) {
      _logger.severe(
        '💥 Error initializing core subscription flow',
        e,
        stackTrace,
      );
      _setError(
        SubscriptionError.unknownError(
          message: 'فشل في تهيئة عملية الاشتراك',
          code: 'INITIALIZATION_ERROR',
          details: {'originalError': e.toString()},
        ),
      );
    }
  }

  /// Update form field with validation using core models
  Future<void> updateFormField({
    String? storeName,
    String? phone,
    String? city,
    String? address,
    String? description,
    String? planType,
    double? price,
  }) async {
    try {
      _logger.info('📝 Updating form field');

      final updatedFormData = state.formData.copyWith(
        storeName: storeName,
        phone: phone,
        city: city,
        address: address,
        description: description,
        planType: planType,
        price: price,
      );

      // Validate using core model validation
      final validatedFormData = await _validateFormData(updatedFormData);

      state = state.copyWith(
        status: CoreSubscriptionFlowStatus.editing,
        formData: validatedFormData,
        progress: _calculateProgress(validatedFormData),
        canNavigateForward: validatedFormData.isValid,
        error: null,
        lastUpdated: DateTime.now(),
      );

      _logger.info('✅ Form field updated successfully');
    } catch (e, stackTrace) {
      _logger.severe('💥 Error updating form field', e, stackTrace);
      _setError(
        SubscriptionError.unknownError(
          message: 'فشل في تحديث البيانات',
          code: 'FORM_UPDATE_ERROR',
          details: {'originalError': e.toString()},
        ),
      );
    }
  }

  /// Submit subscription request using core service
  Future<void> submitSubscription() async {
    if (!state.canSubmit) {
      _logger.warning(
        '⚠️ Cannot submit subscription - form not valid or already submitting',
      );
      return;
    }

    try {
      _logger.info('📤 Submitting core subscription request');

      state = state.copyWith(
        status: CoreSubscriptionFlowStatus.submitting,
        progress: 0.8,
        canNavigateBack: false,
        canNavigateForward: false,
        error: null,
        lastUpdated: DateTime.now(),
      );

      final subscriptionService = ref.read(subscriptionServiceProvider);
      final request = state.formData.toSubscriptionRequest();

      // Call core SubscriptionService
      final result = await subscriptionService.submitSubscriptionRequest(
        request,
      );

      await result.when(
        success: (response) async {
          _logger.info(
            '✅ Core subscription submitted successfully: ${response.id}',
          );

          state = state.copyWith(
            status: CoreSubscriptionFlowStatus.processing,
            progress: 0.9,
            response: response,
            lastUpdated: DateTime.now(),
          );

          // Simulate processing time for better UX
          await Future.delayed(const Duration(seconds: 1));

          state = state.copyWith(
            status: CoreSubscriptionFlowStatus.completed,
            progress: 1.0,
            successMessage: 'Subscription created successfully!',
            successMessageAr: 'تم إنشاء الاشتراك بنجاح!',
            canNavigateForward: true,
            lastUpdated: DateTime.now(),
          );
        },
        failure: (error) async {
          _logger.warning(
            '❌ Core subscription submission failed: ${error.technicalMessage}',
          );

          state = state.copyWith(
            status: CoreSubscriptionFlowStatus.failed,
            canNavigateBack: true,
            canNavigateForward: false,
            error: error,
            lastUpdated: DateTime.now(),
          );
        },
      );
    } catch (e, stackTrace) {
      _logger.severe(
        '💥 Exception during core subscription submission',
        e,
        stackTrace,
      );

      state = state.copyWith(
        status: CoreSubscriptionFlowStatus.failed,
        canNavigateBack: true,
        canNavigateForward: false,
        error: SubscriptionError.unknownError(
          message: 'فشل في إرسال طلب الاشتراك',
          code: 'SUBMISSION_ERROR',
          details: {'originalError': e.toString()},
        ),
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// Cancel subscription flow
  Future<void> cancelFlow() async {
    try {
      _logger.info('❌ Cancelling core subscription flow');

      state = state.copyWith(
        status: CoreSubscriptionFlowStatus.cancelled,
        progress: 0.0,
        error: null,
        successMessage: null,
        successMessageAr: null,
        lastUpdated: DateTime.now(),
      );

      _logger.info('✅ Core subscription flow cancelled successfully');
    } catch (e, stackTrace) {
      _logger.severe(
        '💥 Error cancelling core subscription flow',
        e,
        stackTrace,
      );
    }
  }

  /// Reset flow to initial state
  Future<void> resetFlow() async {
    try {
      _logger.info('🔄 Resetting core subscription flow');

      state = CoreSubscriptionFlowState(lastUpdated: DateTime.now());

      _logger.info('✅ Core subscription flow reset successfully');
    } catch (e, stackTrace) {
      _logger.severe(
        '💥 Error resetting core subscription flow',
        e,
        stackTrace,
      );
    }
  }

  /// Retry failed submission
  Future<void> retrySubmission() async {
    if (state.status != CoreSubscriptionFlowStatus.failed) {
      _logger.warning('⚠️ Cannot retry - flow not in failed state');
      return;
    }

    _logger.info('🔄 Retrying core subscription submission');
    await submitSubscription();
  }

  /// Handle subscription error with core error handler
  Future<void> handleError(SubscriptionError error) async {
    try {
      _logger.warning(
        '⚠️ Handling subscription error: ${error.technicalMessage}',
      );

      // Log error for debugging
      _logger.warning(
        '⚠️ Handling subscription error: ${error.technicalMessage}',
      );

      _setError(error);
    } catch (e, stackTrace) {
      _logger.severe('💥 Error handling subscription error', e, stackTrace);
    }
  }

  // =============================================================================
  // PRIVATE HELPER METHODS
  // =============================================================================

  /// Validate form data using core model validation
  Future<CoreSubscriptionFormData> _validateFormData(
    CoreSubscriptionFormData formData,
  ) async {
    try {
      // Create a temporary SubscriptionRequest to use core validation
      if (formData.userId == null || formData.userId!.isEmpty) {
        return formData.copyWith(
          isValid: false,
          validationErrors: {'userId': 'معرف المستخدم مطلوب'},
        );
      }

      final request = formData.toSubscriptionRequest();
      final validationErrors = request.validate();

      return formData.copyWith(
        isValid: validationErrors.isEmpty,
        validationErrors: validationErrors,
      );
    } catch (e) {
      _logger.warning('⚠️ Error during form validation: $e');
      return formData.copyWith(
        isValid: false,
        validationErrors: {'general': 'خطأ في التحقق من البيانات'},
      );
    }
  }

  /// Calculate progress based on form completion
  double _calculateProgress(CoreSubscriptionFormData formData) {
    int completedFields = 0;
    const int totalFields =
        7; // storeName, phone, city, address, description, planType, price

    if (formData.storeName.isNotEmpty) completedFields++;
    if (formData.phone.isNotEmpty) completedFields++;
    if (formData.city.isNotEmpty) completedFields++;
    if (formData.address.isNotEmpty) completedFields++;
    if (formData.description.isNotEmpty) completedFields++;
    if (formData.planType.isNotEmpty) completedFields++;
    if (formData.price > 0) completedFields++;

    return (completedFields / totalFields) * 0.7; // Max 70% for form completion
  }

  /// Set error state with core SubscriptionError
  void _setError(SubscriptionError error) {
    state = state.copyWith(error: error, lastUpdated: DateTime.now());
  }
}

// =============================================================================
// ADDITIONAL PROVIDERS FOR CORE SUBSCRIPTION FLOW
// =============================================================================

/// Provider for current form validation state
@riverpod
bool isCoreSubscriptionFormValid(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.formData.isValid;
}

/// Provider for current step progress
@riverpod
double coreSubscriptionProgress(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.progress;
}

/// Provider for loading state
@riverpod
bool isCoreSubscriptionLoading(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.isLoading;
}

/// Provider for error state
@riverpod
SubscriptionError? coreSubscriptionError(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.error;
}

/// Provider for Arabic error message
@riverpod
String? coreSubscriptionErrorArabic(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.errorMessageArabic;
}

/// Provider for success state
@riverpod
String? coreSubscriptionSuccess(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.successMessage;
}

/// Provider for Arabic success state
@riverpod
String? coreSubscriptionSuccessArabic(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.successMessageAr;
}

/// Provider for subscription response
@riverpod
SubscriptionResponse? coreSubscriptionResponse(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.response;
}

/// Provider for form data
@riverpod
CoreSubscriptionFormData coreSubscriptionFormData(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.formData;
}

/// Provider for flow status
@riverpod
CoreSubscriptionFlowStatus coreSubscriptionFlowStatus(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.status;
}

/// Provider for validation errors
@riverpod
Map<String, String> coreSubscriptionValidationErrors(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.formData.validationErrors;
}

/// Provider for can submit state
@riverpod
bool canSubmitCoreSubscription(Ref ref) {
  final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
  return flowState.canSubmit;
}
