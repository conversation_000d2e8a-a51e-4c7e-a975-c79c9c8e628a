import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../networking/simple_api_client.dart';
import '../auth/simple_supabase_auth_provider.dart';

part 'seller_request_provider.g.dart';

final _logger = Logger('SellerRequestProvider');

/// Clean Seller Request Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// FIXED: Removed direct Supabase calls
/// ✅ Uses SimpleApiClient ONLY
/// ✅ Uses SimpleAuthSystem for auth

@riverpod
class SellerRequestNotifier extends _$SellerRequestNotifier {
  @override
  bool build() => false;

  /// طلب أن يصبح المستخدم بائع
  /// Request seller status using Go backend API
  Future<bool> requestSellerStatus({
    String sellerType = 'individual',
    String? businessName,
    String? businessAddress,
    String? phoneNumber,
    String? description,
  }) async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      _logger.info('Requesting seller status for user via Go backend');
      final apiClient = ref.read(simpleApiClientProvider);

      final response = await apiClient.post<Map<String, dynamic>>(
        '/seller/request',
        data: {
          'business_type': sellerType,
          if (businessName != null) 'business_name': businessName,
          if (businessAddress != null) 'business_address': businessAddress,
          if (phoneNumber != null) 'phone_number': phoneNumber,
          if (description != null) 'description': description,
        },
      );

      if (!response.isSuccess) {
        throw Exception('Failed to submit seller request: ${response.message}');
      }

      _logger.info('Seller request submitted successfully via Go backend');
      state = true;
      return true;
    } catch (e, st) {
      _logger.severe('Error requesting seller status via Go backend', e, st);
      rethrow;
    }
  }

  /// الموافقة على طلب البيع (للإدارة فقط)
  /// Approve seller request (admin only) using Go backend API
  Future<bool> approveSellerRequest({
    required String sellerId,
    String? notes,
  }) async {
    try {
      _logger.info('Approving seller request: $sellerId via Go backend');
      final apiClient = ref.read(simpleApiClientProvider);

      final response = await apiClient.put<Map<String, dynamic>>(
        '/seller/approve/$sellerId',
        data: {
          'status': 'approved',
          if (notes != null) 'notes': notes,
        },
      );

      if (!response.isSuccess) {
        throw Exception('Failed to approve seller: ${response.message}');
      }

      _logger.info('Seller request approved successfully: $sellerId');
      return true;
    } catch (e, st) {
      _logger.severe('Error approving seller request: $sellerId', e, st);
      rethrow;
    }
  }

  /// رفض طلب البيع (للإدارة فقط)
  /// Reject seller request (admin only) using Go backend API  
  Future<bool> rejectSellerRequest({
    required String sellerId,
    String? reason,
  }) async {
    try {
      _logger.info('Rejecting seller request: $sellerId via Go backend');
      final apiClient = ref.read(simpleApiClientProvider);

      final response = await apiClient.put<Map<String, dynamic>>(
        '/seller/reject/$sellerId',
        data: {
          'status': 'rejected',
          if (reason != null) 'notes': reason,
        },
      );

      if (!response.isSuccess) {
        throw Exception('Failed to reject seller: ${response.message}');
      }

      _logger.info('Seller request rejected successfully: $sellerId');
      return true;
    } catch (e, st) {
      _logger.severe('Error rejecting seller request: $sellerId', e, st);
      rethrow;
    }
  }

  /// الحصول على حالة طلب البيع
  /// Get seller request status using Go backend API
  Future<Map<String, dynamic>?> getSellerStatus() async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        return null;
      }

      final apiClient = ref.read(simpleApiClientProvider);

      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/seller',
      );

      if (!response.isSuccess || response.data == null) {
        return null;
      }

      return response.data!;
    } catch (e) {
      _logger.warning('Error getting seller status: $e');
      return null;
    }
  }
}

// =============================================================================
// REMOVED: Direct Supabase Violations
// =============================================================================
// The following violations have been REMOVED:
// ❌ Direct client.rpc() calls to Supabase
// ❌ Direct database operations from Flutter
// ❌ Complex RPC function calls
//
// Replaced with:
// ✅ SimpleApiClient for ALL data operations
// ✅ Go backend API endpoints
// ✅ Clean error handling
// ✅ Forever Plan compliance
