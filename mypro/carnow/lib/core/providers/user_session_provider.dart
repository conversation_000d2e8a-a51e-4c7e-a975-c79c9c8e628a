// =============================================================================
// User Session Provider - Forever Plan Architecture
// مزود جلسة المستخدم - بنية الخطة الدائمة
// =============================================================================
// Flutter (UI Only) → Go API → Supabase (Data Only)

import '../../features/account/models/user_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../auth/simple_supabase_auth_provider.dart';
import 'users_provider.dart';

part 'user_session_provider.g.dart';

enum UserSessionState {
  loading,
  unauthenticated,
  authenticatedIncompleteProfile,
  authenticatedCompleteProfile,
  error,
}

@riverpod
UserSessionState userSessionState(Ref ref) {
  // Keep this critical provider alive to prevent premature disposal
  ref.keepAlive();
  
  // Watch authentication state using convenience providers
  final authState = ref.watch(simpleSupabaseAuthProvider);
  final currentUser = ref.watch(currentUserProvider);
  
  // Handle loading state
  if (authState is SimpleAuthStateLoading) {
    return UserSessionState.loading;
  }
  
  // Handle error state
  if (authState is SimpleAuthStateError) {
    // Check if this is a persistent error or just initialization issue
    final errorMessage = authState.message.toLowerCase();
    final isPersistentError = errorMessage.contains('invalid') ||
                             errorMessage.contains('expired') ||
                             errorMessage.contains('unauthorized') ||
                             errorMessage.contains('forbidden');
    
    if (isPersistentError) {
      return UserSessionState.error;
    } else {
      // For network/initialization errors, treat as unauthenticated
      return UserSessionState.unauthenticated;
    }
  }
  
  // Handle unauthenticated state
  if (authState is SimpleAuthStateUnauthenticated || currentUser == null) {
    return UserSessionState.unauthenticated;
  }
  
  // For authenticated users, check profile completeness
  // Watch the current user stream to get UserModel
  final currentUserAsync = ref.watch(currentUserStreamProvider);
  
  return currentUserAsync.when(
    data: (user) {
      if (user == null) {
        return UserSessionState.unauthenticated;
      }
      
      if (_isProfileComplete(user)) {
        return UserSessionState.authenticatedCompleteProfile;
      } else {
        return UserSessionState.authenticatedIncompleteProfile;
      }
    },
    loading: () => UserSessionState.loading,
    error: (error, stackTrace) {
      // FIXED: Don't show error state for user data loading issues
      // If auth is successful but user data loading fails, still show as authenticated
      if (authState is SimpleAuthStateAuthenticated) {
        // Create a basic UserModel from auth data
        final basicUser = UserModel(
          id: currentUser.id,
          authId: currentUser.id,
          email: currentUser.email,
          name: currentUser.name,
          phone: currentUser.phone,
          profileImageUrl: currentUser.profileImageUrl,
          createdAt: currentUser.createdAt,
          updatedAt: currentUser.updatedAt,
        );
        
        if (_isProfileComplete(basicUser)) {
          return UserSessionState.authenticatedCompleteProfile;
        } else {
          return UserSessionState.authenticatedIncompleteProfile;
        }
      }
      
      // Only return error if we have no auth data at all
      return UserSessionState.unauthenticated;
    },
  );
}

// Helper method to check if profile is complete
bool _isProfileComplete(UserModel user) {
  final hasEmail = user.email?.trim().isNotEmpty ?? false;
  final hasName = user.name?.trim().isNotEmpty ?? false;
  
  // For OAuth users, phone is optional initially
  // They can add it later in profile settings
  
  // Profile is complete if user has email and name
  // Phone is optional for OAuth users to reduce friction
  return hasEmail && hasName;
}

// =============================================================================
// Additional Session Helpers - مساعدات الجلسة الإضافية
// =============================================================================

/// Quick check if user is authenticated
@riverpod
bool isUserAuthenticated(Ref ref) {
  final sessionState = ref.watch(userSessionStateProvider);
  return sessionState == UserSessionState.authenticatedCompleteProfile ||
         sessionState == UserSessionState.authenticatedIncompleteProfile;
}

/// Check if user has complete profile
@riverpod  
bool hasCompleteProfile(Ref ref) {
  final sessionState = ref.watch(userSessionStateProvider);
  return sessionState == UserSessionState.authenticatedCompleteProfile;
}

/// Get session state description
@riverpod
String sessionStateDescription(Ref ref) {
  final state = ref.watch(userSessionStateProvider);
  switch (state) {
    case UserSessionState.loading:
      return 'جاري التحميل...';
    case UserSessionState.unauthenticated:
      return 'غير مسجل دخول';
    case UserSessionState.authenticatedIncompleteProfile:
      return 'الملف الشخصي غير مكتمل';
    case UserSessionState.authenticatedCompleteProfile:
      return 'مسجل دخول';
    case UserSessionState.error:
      return 'خطأ في الجلسة';
  }
}
