
import 'package:flutter/foundation.dart';

/// Production-ready configuration for CarNow app
class ProductionConfig {
  // Environment detection
  static bool get isProduction => kReleaseMode;
  static bool get isDevelopment => kDebugMode;
  static bool get isStaging => kProfileMode;

  // Server URLs - Forever Plan Architecture
  static const String _prodBaseUrl = 'https://backend-go-8klm.onrender.com';
  static const String _stagingBaseUrl = 'https://backend-go-8klm.onrender.com';
  // Always use hosted backend for consistency
  static const String _devBaseUrl = 'https://backend-go-8klm.onrender.com';
  static const String _androidEmulatorBaseUrl = 'https://backend-go-8klm.onrender.com';

  /// Get the appropriate base URL for current environment
  static String get baseUrl {
    // Always use hosted backend for now
    return _prodBaseUrl;
  }

  // API Configuration
  static const String apiVersion = 'v1';
  static String get apiBaseUrl => '$baseUrl/api/$apiVersion';

  // Performance Settings (optimized for production)
  static Duration get requestTimeout => isProduction 
    ? const Duration(seconds: 10)
    : const Duration(seconds: 30);
    
  static Duration get connectTimeout => isProduction
    ? const Duration(seconds: 5) 
    : const Duration(seconds: 15);

  // Retry Configuration (production optimized)
  static int get maxRetries => isProduction ? 2 : 3;
  static Duration get retryDelay => const Duration(seconds: 1);

  // Cache Settings
  static Duration get cacheExpiry => isProduction
    ? const Duration(minutes: 10)
    : const Duration(minutes: 5);
    
  static int get maxCacheSize => isProduction ? 200 : 100;

  // Monitoring Settings
  static bool get enablePerformanceMonitoring => true;
  static bool get enableErrorTracking => true;
  static bool get enableHealthChecks => isProduction;
  
  static Duration get healthCheckInterval => const Duration(minutes: 5);
  static Duration get performanceWarningThreshold => const Duration(seconds: 2);
  static Duration get performanceCriticalThreshold => const Duration(seconds: 5);

  // Security Settings
  static bool get enableRateLimiting => isProduction;
  static int get rateLimitRequests => 1000;
  static Duration get rateLimitWindow => const Duration(minutes: 15);

  // Logging Configuration
  static String get logLevel => isProduction ? 'INFO' : 'DEBUG';
  static bool get enableVerboseLogging => !isProduction;
  static bool get enablePerformanceLogs => true;

  // Database Pool Settings (for backend reference)
  static const Map<String, dynamic> databaseConfig = {
    'pool_size': 20,
    'max_connections': 100,
    'timeout': '30s',
    'retry_attempts': 3,
    'connection_lifetime': '3600s',
  };

  // Feature Flags
  static bool get enableAdvancedCaching => isProduction;
  static bool get enablePredictiveLoading => isProduction;
  static bool get enableBackgroundSync => isProduction;

  // Build all URLs for endpoints
  static String buildApiUrl(String endpoint) {
    return '$apiBaseUrl$endpoint';
  }

  static String buildHealthUrl() {
    return '$baseUrl/health';
  }

  static String buildReadyUrl() {
    return '$baseUrl/ready';
  }

  /// Get current environment information
  static Map<String, dynamic> getEnvironmentInfo() {
    return {
      'environment': isProduction ? 'production' : (isStaging ? 'staging' : 'development'),
      'base_url': baseUrl,
      'api_url': apiBaseUrl,
      'request_timeout': requestTimeout.inMilliseconds,
      'connect_timeout': connectTimeout.inMilliseconds,
      'max_retries': maxRetries,
      'cache_expiry': cacheExpiry.inMinutes,
      'performance_monitoring': enablePerformanceMonitoring,
      'health_checks': enableHealthChecks,
    };
  }

  /// Validate production readiness
  static Map<String, bool> validateProductionReadiness() {
    return {
      'environment_check': isProduction,
      'url_configured': baseUrl == _prodBaseUrl,
      'timeouts_optimized': requestTimeout.inSeconds <= 10,
      'monitoring_enabled': enablePerformanceMonitoring,
      'health_checks_enabled': enableHealthChecks,
      'caching_enabled': enableAdvancedCaching,
      'security_enabled': enableRateLimiting,
    };
  }
}

/// Production-specific CarnowBackend configuration
class ProductionCarnowConfig {
  static String get baseUrl => ProductionConfig.baseUrl;
  static String get apiBaseUrl => ProductionConfig.apiBaseUrl;
  
  // Optimized timeouts for production
  static Duration get requestTimeout => ProductionConfig.requestTimeout;
  static Duration get connectTimeout => ProductionConfig.connectTimeout;
  
  // Production endpoints
  static const String healthEndpoint = '/health';
  static const String readyEndpoint = '/ready';
  
  // User endpoints
  static const String usersEndpoint = '/users';
  static const String userByEmailEndpoint = '/users/email';
  
  // Wallet endpoints
  static const String walletsEndpoint = '/wallets';
  static const String walletByUserIdEndpoint = '/wallets/user';
  static const String depositEndpoint = '/deposit';
  static const String withdrawEndpoint = '/withdraw';
  static const String transactionsEndpoint = '/transactions';
  
  // Financial operations endpoints
  static const String operationsEndpoint = '/operations';
  static const String processOperationEndpoint = '/process';
  static const String userOperationsEndpoint = '/operations/user';
  
  // Build full URL for endpoint
  static String buildUrl(String endpoint) {
    if (endpoint.startsWith('/admin/')) {
      return '$baseUrl$endpoint';
    }
    return '$apiBaseUrl$endpoint';
  }

  // Get production-ready headers
  static Map<String, String> getProductionHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'CarNow-Flutter/${ProductionConfig.isProduction ? "Production" : "Development"}',
      if (ProductionConfig.enableRateLimiting)
        'X-Client-Version': '1.0.0',
    };
  }
} 