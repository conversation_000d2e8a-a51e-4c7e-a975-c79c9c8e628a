import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';
import 'server_urls.dart';

/// Simple and fast backend configuration
/// Checks local server once at startup, then uses cached result
class BackendConfig {
  // ==================== CONFIGURATION (Easy to change) ====================
  
  /// Health check timeout for server discovery (increased for Android emulator)
  static const Duration _healthCheckTimeout = Duration(milliseconds: 3000);
  
  /// Cache the discovered server URL (set once at startup)
  static String? _cachedServerUrl;
  static bool _hasCheckedLocal = false;

  // ==================== FAST SERVER DISCOVERY ====================
  
  /// Get the best available backend URL (fast, cached)
  static String get baseUrl {
    // Check for environment variable override first
    const apiBaseUrl = String.fromEnvironment('API_BASE_URL');
    if (apiBaseUrl.isNotEmpty) {
      debugPrint('🔧 Using API_BASE_URL from environment: $apiBaseUrl');
      return apiBaseUrl;
    }

    // Return cached result if available
    if (_cachedServerUrl != null) {
      return _cachedServerUrl!;
    }

    // In debug mode, we need to check servers synchronously first time
    if (!_hasCheckedLocal && kDebugMode) {
      _hasCheckedLocal = true;

      // Try to detect available server synchronously
      final availableServer = _detectAvailableServerSync();
      if (availableServer != null) {
        _cachedServerUrl = availableServer;
        debugPrint('🔍 Detected and using server: $availableServer');
        return availableServer;
      }
    }

    // Return hosted server as fallback
    _cachedServerUrl = ServerUrls.productionBackend;
    debugPrint('🔍 Using hosted server as fallback: ${ServerUrls.productionBackend}');
    return ServerUrls.productionBackend;
  }
  
  /// Detect available server once at startup (no switching later)
  static String? _detectAvailableServerSync() {
    debugPrint('🔍 Simple fallback detection: Local → Hosted');

    // Try local first (fast check)
    try {
      final localUrl = ServerUrls.androidEmulatorBackend;
      // Quick sync check - if local server responds immediately, use it
      debugPrint('🔍 Quick check: $localUrl');
      return localUrl; // Will be validated async later
    } catch (e) {
      debugPrint('🔍 Local not available, using hosted: ${ServerUrls.productionBackend}');
      return ServerUrls.productionBackend;
    }
  }

  /// Check local server once at startup (non-blocking)
  static void _checkLocalServerOnce() {
    _hasCheckedLocal = true;

    // Quick check in background
    _tryLocalServer().then((localUrl) {
      if (localUrl != null) {
        _cachedServerUrl = localUrl;
        debugPrint('🔍 Using local server: $localUrl');
      } else {
        _cachedServerUrl = ServerUrls.productionBackend;
        debugPrint('🔍 Using hosted server: ${ServerUrls.productionBackend}');
      }
    }).catchError((e) {
      _cachedServerUrl = ServerUrls.productionBackend;
      debugPrint('🔍 Using hosted server (error): ${ServerUrls.productionBackend}');
    });
  }
  
  /// Try to connect to local server (fast timeout)
  static Future<String?> _tryLocalServer() async {
    final localUrls = [
      ServerUrls.androidEmulatorBackend, // Try Android emulator first
      ServerUrls.localBackend,
    ];

    debugPrint('🔍 Actually testing if local servers are running...');

    for (final url in localUrls) {
      try {
        debugPrint('🔍 Testing if $url is actually running...');
        final isHealthy = await _checkServerHealth(url);
        if (isHealthy) {
          debugPrint('✅ Local server is actually running and healthy: $url');
          return url;
        } else {
          debugPrint('❌ Local server $url is not running or not healthy');
        }
      } catch (e) {
        debugPrint('❌ Local server $url is not running: $e');
        // Continue to next URL
      }
    }

    debugPrint('🔍 No local servers are actually running');
    return null;
  }
  
  /// Check if server is healthy (very fast)
  static Future<bool> _checkServerHealth(String baseUrl) async {
    try {
      final healthUrl = ServerUrls.buildHealthUrl(baseUrl);
      debugPrint('🔍 Health check URL: $healthUrl');

      final response = await http.get(
        Uri.parse(healthUrl),
        headers: {'Accept': 'application/json'},
      ).timeout(_healthCheckTimeout);

      final isHealthy = response.statusCode == 200;
      debugPrint('🔍 Health check result for $baseUrl: ${isHealthy ? "✅ Healthy" : "❌ Not healthy (${response.statusCode})"}');
      return isHealthy;
    } catch (e) {
      debugPrint('🔍 Health check failed for $baseUrl: $e');
      return false;
    }
  }


  
  /// Clear server cache (useful for testing)
  static void clearCache() {
    _cachedServerUrl = null;
    _hasCheckedLocal = false;
  }

  /// Detect server once at startup (no switching during runtime)
  static Future<String> detectBestServer() async {
    debugPrint('🔍 Detecting server once at startup...');

    // Always start with hosted server
    _cachedServerUrl = ServerUrls.productionBackend;

    // In debug mode, try local servers and update cache if available
    if (kDebugMode) {
      debugPrint('🔍 Debug mode: checking if local servers are actually running...');
      final localUrl = await _tryLocalServer();
      if (localUrl != null) {
        _cachedServerUrl = localUrl;
        debugPrint('✅ Local server is running and will be used permanently: $localUrl');
        return localUrl;
      }
      debugPrint('🔍 No local servers are running, staying with hosted server');
    }

    // Use hosted server permanently
    debugPrint('✅ Using hosted server permanently: ${ServerUrls.productionBackend}');
    return ServerUrls.productionBackend;
  }

  // ==================== URL BUILDERS ====================
  
  /// Build full API URL
  static String buildApiUrl(String endpoint) {
    final base = baseUrl;
    return ServerUrls.buildApiUrl(base, endpoint);
  }
  
  /// Get health check URL
  static String get healthUrl {
    final base = baseUrl;
    return ServerUrls.buildHealthUrl(base);
  }
  
  /// Get ready check URL
  static String get readyUrl {
    final base = baseUrl;
    return ServerUrls.buildReadyUrl(base);
  }

  // ==================== LEGACY SUPPORT ====================
  
  /// Legacy getter for backward compatibility
  static String get baseUrlSync {
    return baseUrl;
  }
  
  /// Legacy URL builder for backward compatibility
  static String buildUrl(String endpoint) {
    return buildApiUrl(endpoint);
  }
  
  /// Request timeout duration for backward compatibility
  static const Duration requestTimeout = Duration(seconds: 30);

  // ==================== API ENDPOINTS ====================
  
  // Seller endpoints
  static const String sellerApplicationsEndpoint = '/seller-applications';
  static const String sellersEndpoint = '/sellers';
  static const String sellerStoreEndpoint = '/sellers/store';
  static const String sellerProfileEndpoint = '/sellers/profile';
  static const String sellerSubscriptionsEndpoint = '/sellers/subscriptions';

  // ==================== CONFIGURATION HELPERS ====================
  
  /// Get current server information
  static Map<String, dynamic> getServerInfo() {
    return {
      'cached_url': _cachedServerUrl,
      'has_checked_local': _hasCheckedLocal,
      'hosted_url': ServerUrls.productionBackend,
      'local_urls': [ServerUrls.localBackend, ServerUrls.androidEmulatorBackend],
      'health_check_timeout_ms': _healthCheckTimeout.inMilliseconds,
    };
  }
  
  /// Force refresh server discovery
  static Future<String> refreshServerDiscovery() async {
    clearCache();
    _checkLocalServerOnce();
    return baseUrl;
  }

  /// Debug method to clear authentication when switching between local and hosted servers
  /// This is needed because JWT tokens from hosted server won't work with local server
  static Future<void> clearAuthenticationForServerSwitch() async {
    try {
      debugPrint('🧹 Clearing authentication tokens for server switch...');

      // Create secure storage instance
      const storage = FlutterSecureStorage();

      // Clear all authentication-related keys
      await storage.deleteAll();

      debugPrint('✅ Authentication tokens cleared successfully');
      debugPrint('💡 Please restart the app and sign in again');
    } catch (e) {
      debugPrint('❌ Failed to clear authentication tokens: $e');
    }
  }
  
  /// Print current server configuration
  static void printServerConfiguration() {
    print('🔗 Current Backend Configuration:');
    print('   Cached URL: ${_cachedServerUrl ?? 'None'}');
    print('   Has Checked Local: $_hasCheckedLocal');
    print('   Hosted URL: ${ServerUrls.productionBackend}');
    print('   Local URLs: ${[ServerUrls.localBackend, ServerUrls.androidEmulatorBackend]}');
    ServerUrls.printConfiguration();
  }
}

/// Configuration for CarNow Backend (Go) integration
class CarnowBackendConfig {
  /// Get the current backend base URL
  static String get baseUrl {
    return BackendConfig.baseUrl;
  }
  
  /// Fallback URL for immediate access
  static String get fallbackUrl {
    return BackendConfig.baseUrlSync;
  }
  
  /// Synchronous base URL for backward compatibility
  static String get baseUrlSync {
    return BackendConfig.baseUrlSync;
  }

  /// API version
  static String get apiVersion => ServerUrls.apiVersion;

  /// Full API base URL
  static String get apiBaseUrl {
    final base = baseUrl;
    return '$base${ServerUrls.apiBasePath}';
  }
  
  /// Synchronous API base URL for backward compatibility
  static String get apiBaseUrlSync {
    final base = baseUrlSync;
    return '$base${ServerUrls.apiBasePath}';
  }

  /// Request timeout duration
  static const Duration requestTimeout = Duration(seconds: 30);

  /// API Endpoints
  static const String healthEndpoint = '/health';
  static const String publicHealthEndpoint = '/health';

  // User endpoints
  static const String usersEndpoint = '/users';
  static const String createUserEndpoint = '/users';
  static const String getUserEndpoint = '/users';

  // Wallet endpoints
  static const String walletEndpoint = '/wallets';
  static const String updateWalletEndpoint = '/wallets';
  static const String walletTransactionsEndpoint = '/wallets';
  static const String getWalletTransactionsEndpoint = '/wallets/transactions';

  // Product endpoints
  static const String productsEndpoint = '/products';
  static const String productsSimpleEndpoint = '/products-simple';
  static const String productDetailsEndpoint = '/products';
  static const String searchProductsEndpoint = '/products/search';
  static const String categoriesEndpoint = '/categories';
  static const String brandsEndpoint = '/brands';

  // Order endpoints
  static const String ordersEndpoint = '/orders';
  static const String createOrderEndpoint = '/orders';
  static const String orderHistoryEndpoint = '/orders/history';
  static const String orderStatusEndpoint = '/orders';

  // Cart endpoints
  static const String cartEndpoint = '/cart';
  static const String addToCartEndpoint = '/cart/items';
  static const String removeFromCartEndpoint = '/cart/items';
  static const String updateCartItemEndpoint = '/cart/items';
  static const String clearCartEndpoint = '/cart/clear';

  // Authentication endpoints
  static const String authEndpoint = '/auth';
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String refreshTokenEndpoint = '/auth/refresh';
  static const String logoutEndpoint = '/auth/logout';

  // Profile endpoints
  static const String profileEndpoint = '/profile';
  static const String updateProfileEndpoint = '/profile';
  static const String changePasswordEndpoint = '/profile/password';

  // Notification endpoints
  static const String notificationsEndpoint = '/notifications';
  static const String markNotificationReadEndpoint = '/notifications/read';

  // Search and filter endpoints
  static const String searchEndpoint = '/search';
  static const String filtersEndpoint = '/filters';
  static const String suggestionsEndpoint = '/suggestions';

  // Admin endpoints
  static const String adminEndpoint = '/admin';
  static const String adminUsersEndpoint = '/admin/users';
  static const String adminOrdersEndpoint = '/admin/orders';
  static const String adminProductsEndpoint = '/admin/products';
  static const String adminAnalyticsEndpoint = '/admin/analytics';

  // Seller endpoints
  static const String sellerApplicationsEndpoint = '/seller-applications';
  static const String sellersEndpoint = '/sellers';
  static const String sellerStoreEndpoint = '/sellers/store';
  static const String sellerProfileEndpoint = '/sellers/profile';
  static const String sellerSubscriptionsEndpoint = '/sellers/subscriptions';

  // Utility endpoints
  static const String uploadEndpoint = '/upload';
  static const String downloadEndpoint = '/download';
  static const String exportEndpoint = '/export';
  static const String importEndpoint = '/import';

  /// Build URL for endpoint
  static String buildUrl(String endpoint) {
    final base = baseUrl;
    return ServerUrls.buildApiUrl(base, endpoint);
  }
  
  /// Synchronous build URL for backward compatibility
  static String buildUrlSync(String endpoint) {
    final base = baseUrlSync;
    return ServerUrls.buildApiUrl(base, endpoint);
  }
  
  /// Google OAuth URL for backward compatibility
  static String get googleOAuthUrl => '$apiBaseUrlSync/auth/google';
}

// -----------------------------------------------------------------------------
// إعدادات Supabase
// -----------------------------------------------------------------------------

class SupabaseConfig {
  SupabaseConfig._();

  // Carnow (general) project – used only for authentication when required
  static const String carnowUrl = 'https://lpxtghyvxuenyyisrrro.supabase.co';
  static const String carnowAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2MjIxNzgsImV4cCI6MjA2ODE5ODE3OH0.7GQrSfJwLQskYOcu9nusXF1amMnezpw3s96sk-zHQ64';
}

// -----------------------------------------------------------------------------
// إعدادات المصادقة والأمان
// -----------------------------------------------------------------------------

class AuthConfig {
  AuthConfig._();

  /// Default currency for Carnow wallet operations.
  static const String defaultCurrency = 'LYD';
}
