import 'dart:async';
import 'dart:collection';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../auth/simple_supabase_auth_provider.dart';
import '../utils/unified_logger.dart';
import '../utils/resource_manager.dart';

final _logger = Logger('AnalyticsService');

/// Analytics Service Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses Go Backend API ONLY for analytics
/// ✅ NO direct Supabase calls
/// ✅ Clean async/await patterns
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return AnalyticsService(ref);
});

/// Analytics Service - Forever Plan compliant
/// 
/// Sends analytics events to Go backend for processing and storage
class AnalyticsService {
  AnalyticsService(this._ref) {
    _startPeriodicFlush();
  }

  final Ref _ref;
  final Queue<AnalyticsEvent> _eventQueue = Queue<AnalyticsEvent>();
  Timer? _flushTimer;

  /// Get SimpleApiClient for making requests to Go backend
  SimpleApiClient get _apiClient => _ref.read(simpleApiClientProvider);

  /// Start periodic flush of events to Go backend
  void _startPeriodicFlush() {
    _flushTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _flushEvents();
    });
  }

  /// Log an analytics event to the queue
  Future<void> logEvent(
    String eventType, {
    String? entityType,
    String? entityId,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final event = AnalyticsEvent(
        eventType: eventType,
        entityType: entityType,
        entityId: entityId,
        properties: properties ?? {},
        timestamp: DateTime.now(),
      );

      _eventQueue.add(event);

      UnifiedLogger.info(
        'Analytics event queued: $eventType',
        tag: 'Analytics',
      );

      // Flush immediately for critical events
      if (_isCriticalEvent(eventType)) {
        await _flushEvents();
      }
    } catch (e) {
      UnifiedLogger.error(
        'Failed to queue analytics event: $e',
        error: e,
        tag: 'Analytics',
      );
    }
  }

  /// Check if event is critical and should be sent immediately
  bool _isCriticalEvent(String eventType) {
    const criticalEvents = {
      'purchase',
      'user_registration',
      'login',
      'logout',
      'error',
      'crash',
    };
    return criticalEvents.contains(eventType);
  }

  /// Flush queued events to Go backend
  Future<void> _flushEvents() async {
    if (_eventQueue.isEmpty) return;

    final events = _eventQueue.toList();
    _eventQueue.clear();

    try {
      final currentUser = _ref.read(currentUserProvider);
      
      // Prepare events for backend
      final eventPayloads = events.map((event) => {
        'event_type': event.eventType,
        'entity_type': event.entityType,
        'entity_id': event.entityId,
        'properties': event.properties,
        'timestamp': event.timestamp.toIso8601String(),
        'user_id': currentUser?.id,
      }).toList();

      _logger.info('Flushing ${events.length} analytics events to Go backend');

      // Send events to Go backend
      final response = await _apiClient.postApi<Map<String, dynamic>>(
        '/analytics/events',
        data: {
          'events': eventPayloads,
          'batch_id': DateTime.now().millisecondsSinceEpoch.toString(),
        },
      );

      if (response.isSuccess) {
        UnifiedLogger.info(
          'Successfully sent ${events.length} analytics events',
          tag: 'Analytics',
        );
      } else {
        _logger.warning('Failed to send analytics events: ${response.message}');
        // Re-add events to queue on failure
        for (final event in events) {
          _eventQueue.addFirst(event);
        }
      }
    } catch (e) {
      UnifiedLogger.error(
        'Failed to flush analytics events: $e',
        error: e,
        tag: 'Analytics',
      );
      // Re-add events to queue on failure
      for (final event in events) {
        _eventQueue.addFirst(event);
      }
    }
  }

  // Specific event logging methods
  Future<void> logProductView(String productId) async {
    await logEvent(
      'product_view', 
      entityType: 'product', 
      entityId: productId,
      properties: {
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logSearch(String query, int resultCount) async {
    await logEvent(
      'search',
      properties: {
        'query': query, 
        'result_count': resultCount,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logPurchase(String orderId, double amount) async {
    await logEvent(
      'purchase',
      entityType: 'order',
      entityId: orderId,
      properties: {
        'amount': amount,
        'currency': 'SAR',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logUserAction(
    String action, {
    Map<String, dynamic>? data,
  }) async {
    await logEvent(
      'user_action', 
      properties: {
        'action': action, 
        ...?data,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Log page view
  Future<void> logPageView(String pageName) async {
    await logEvent(
      'page_view',
      properties: {
        'page_name': pageName,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Log user registration
  Future<void> logUserRegistration(String userId, String method) async {
    await logEvent(
      'user_registration',
      entityType: 'user',
      entityId: userId,
      properties: {
        'registration_method': method,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Log user login
  Future<void> logUserLogin(String userId, String method) async {
    await logEvent(
      'login',
      entityType: 'user',
      entityId: userId,
      properties: {
        'login_method': method,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Log error events
  Future<void> logError(String errorType, String errorMessage, {
    String? context,
    Map<String, dynamic>? additionalData,
  }) async {
    await logEvent(
      'error',
      properties: {
        'error_type': errorType,
        'error_message': errorMessage,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
        ...?additionalData,
      },
    );
  }

  /// Force flush all pending events
  Future<void> forceFlush() async {
    await _flushEvents();
  }

  void dispose() {
    // Flush any remaining events before disposing
    _flushEvents();
    
    ResourceManager.cancelTimer('analytics_flush');
    _flushTimer?.cancel();
    _flushTimer = null;
    
    UnifiedLogger.info('📊 Analytics service disposed', tag: 'Analytics');
  }
}

/// Analytics Event model
class AnalyticsEvent {
  AnalyticsEvent({
    required this.eventType,
    this.entityType,
    this.entityId,
    required this.properties,
    required this.timestamp,
  });
  
  final String eventType;
  final String? entityType;
  final String? entityId;
  final Map<String, dynamic> properties;
  final DateTime timestamp;
}
