/// =============================================================================
/// STORAGE SERVICE - Forever Plan Architecture
/// =============================================================================
/// 
/// File Upload/Download Service following Forever Plan Architecture:
/// Flutter (UI Only) → Go API → Supabase Storage (Data Only)
/// 
/// ✅ Uses Go Backend API ONLY for all file operations
/// ✅ NO direct Supabase Storage calls
/// ✅ Clean async/await patterns
/// =============================================================================

import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:dio/dio.dart';

import '../networking/simple_api_client.dart';
import '../errors/app_error.dart';
import '../auth/simple_supabase_auth_provider.dart';

import '../config/backend_config.dart';

final _logger = Logger('StorageService');

/// Provider for StorageService - Forever Plan compliant
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService(ref);
});

/// Service for file operations through Go backend API
/// 
/// All file operations go through Go backend which handles Supabase Storage internally
/// No direct Supabase Storage calls from Flutter
class StorageService {
  StorageService(this._ref);
  
  final Ref _ref;

  /// Get SimpleApiClient for making requests to Go backend
  SimpleApiClient get _apiClient => _ref.read(simpleApiClientProvider);

  /// Check if user is authenticated using convenience provider
  bool get _isAuthenticated => _ref.read(isAuthenticatedProvider);

  /// Upload a file through Go backend using proper Dio FormData
  /// 
  /// [filePath]: Path to the local file
  /// [bucket]: Target bucket (product_images, avatars, documents, etc.)
  /// [fileName]: Optional custom file name
  /// 
  /// Returns the public URL of the uploaded file
  Future<String> uploadFile({
    required String filePath,
    required String bucket,
    String? fileName,
    Map<String, String>? metadata,
  }) async {
    try {
      _logger.info('Uploading file through Go backend: $filePath');

      if (!_isAuthenticated) {
        throw const AppError.authentication(message: 'User must be authenticated to upload files');
      }

      final file = File(filePath);
      if (!await file.exists()) {
        throw const AppError.validation(message: 'File does not exist');
      }

      // Validate file type
      final mimeType = lookupMimeType(filePath);
      if (mimeType == null) {
        throw const AppError.validation(message: 'Could not determine file type');
      }

      // Generate file name if not provided
      final finalFileName = fileName ?? 
          '${DateTime.now().millisecondsSinceEpoch}${path.extension(filePath)}';

      // Create multipart form data using Dio's FormData
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          filePath,
          filename: finalFileName,
        ),
        'bucket': bucket,
        'filename': finalFileName,
        if (metadata != null) ...metadata,
      });

      // Use direct Dio call for file upload since SimpleApiClient doesn't handle FormData
      final dio = Dio();
      final authState = _ref.read(simpleSupabaseAuthProvider);
      if (authState is SimpleAuthStateAuthenticated) {
        dio.options.headers['Authorization'] = 'Bearer ${authState.token}';
      }
      
      final response = await dio.post(
        '${BackendConfig.baseUrl}/storage/upload',
        data: formData,
      );

      if (response.statusCode != 200 || response.data == null) {
        throw const NetworkException('Failed to upload file');
      }

      final publicUrl = response.data['public_url'] as String?;
      if (publicUrl == null) {
        throw const NetworkException('No public URL returned from upload');
      }

      _logger.info('File uploaded successfully: $publicUrl');
      return publicUrl;
    } catch (e) {
      _logger.severe('Error uploading file: $e');
      rethrow;
    }
  }

  /// Upload product image through Go backend
  /// 
  /// [filePath]: Path to the image file
  /// [productId]: Optional product ID for organized naming
  /// 
  /// Returns the public URL of the uploaded image
  Future<String> uploadProductImage({
    required String filePath,
    String? productId,
  }) async {
    try {
      _logger.info('Uploading product image through Go backend');

      // Validate image file
      final mimeType = lookupMimeType(filePath);
      if (mimeType == null || !mimeType.startsWith('image/')) {
        throw const AppError.validation(
          message: 'Invalid file type. Only images are allowed.',
        );
      }

      // Generate organized file name
      final fileExt = path.extension(filePath).toLowerCase();
      final fileName = productId != null
          ? 'product_${productId}_${DateTime.now().millisecondsSinceEpoch}$fileExt'
          : 'product_${DateTime.now().millisecondsSinceEpoch}$fileExt';

      return await uploadFile(
        filePath: filePath,
        bucket: 'product_images',
        fileName: fileName,
        metadata: {
          'type': 'product_image',
          if (productId != null) 'product_id': productId,
        },
      );
    } catch (e) {
      _logger.severe('Error uploading product image: $e');
      rethrow;
    }
  }

  /// Upload user profile image through Go backend
  /// 
  /// [filePath]: Path to the image file
  /// [userId]: User ID for organized naming
  /// 
  /// Returns the public URL of the uploaded avatar
  Future<String> uploadUserProfileImage({
    required String filePath,
    required String userId,
  }) async {
    try {
      _logger.info('Uploading user profile image through Go backend');

      // Validate image file
      final mimeType = lookupMimeType(filePath);
      if (mimeType == null || !mimeType.startsWith('image/')) {
        throw const AppError.validation(
          message: 'Invalid file type. Only images are allowed.',
        );
      }

      final fileExt = path.extension(filePath).toLowerCase();
      final fileName = 'avatar_${userId}$fileExt';

      return await uploadFile(
        filePath: filePath,
        bucket: 'avatars',
        fileName: fileName,
        metadata: {
          'type': 'avatar',
          'user_id': userId,
        },
      );
    } catch (e) {
      _logger.severe('Error uploading user profile image: $e');
      rethrow;
    }
  }

  /// Upload seller document through Go backend
  /// 
  /// [filePath]: Path to the document file
  /// [documentType]: Type of document (license, certification, etc.)
  /// 
  /// Returns the public URL of the uploaded document
  Future<String> uploadSellerDocument({
    required String filePath,
    required String documentType,
  }) async {
    try {
      _logger.info('Uploading seller document through Go backend: $documentType');

      // Check authentication and get current user
      final currentUser = _ref.read(currentUserProvider);
      final fileExt = path.extension(filePath).toLowerCase();
      final fileName = 
          'seller_${currentUser?.id}_${documentType}_${DateTime.now().millisecondsSinceEpoch}$fileExt';

      return await uploadFile(
        filePath: filePath,
        bucket: 'seller_documents',
        fileName: fileName,
        metadata: {
          'type': 'seller_document',
          'document_type': documentType,
          'user_id': currentUser?.id ?? 'unknown',
        },
      );
    } catch (e) {
      _logger.severe('Error uploading seller document: $e');
      rethrow;
    }
  }

  /// Delete a file through Go backend
  /// 
  /// [fileUrl]: Public URL of the file to delete
  /// 
  /// Returns true if deletion was successful
  Future<bool> deleteFile(String fileUrl) async {
    try {
      _logger.info('Deleting file through Go backend: $fileUrl');

      if (!_isAuthenticated) {
        throw const AppError.authentication(message: 'User must be authenticated to delete files');
      }

      final response = await _apiClient.deleteApi(
        '/storage/delete',
        queryParameters: {'file_url': fileUrl},
      );

      final success = response.isSuccess;
      _logger.info('File deletion result: $success');
      return success;
    } catch (e) {
      _logger.severe('Error deleting file: $e');
      return false;
    }
  }

  /// Delete product image through Go backend
  /// 
  /// [imageUrl]: Public URL of the image to delete
  /// 
  /// Returns true if deletion was successful
  Future<bool> deleteProductImage(String imageUrl) async {
    try {
      _logger.info('Deleting product image through Go backend');
      return await deleteFile(imageUrl);
    } catch (e) {
      _logger.severe('Error deleting product image: $e');
      return false;
    }
  }

  /// Get a signed URL for temporary file access through Go backend
  /// 
  /// [filePath]: Path to the file in storage
  /// [expiresIn]: Duration for which the URL should be valid
  /// 
  /// Returns a signed URL for temporary access
  Future<String?> getSignedUrl({
    required String filePath,
    Duration expiresIn = const Duration(hours: 1),
  }) async {
    try {
      _logger.info('Getting signed URL through Go backend: $filePath');

      final response = await _apiClient.getApi<Map<String, dynamic>>(
        '/storage/url',
        queryParameters: {
          'file_path': filePath,
          'expires_in': expiresIn.inSeconds.toString(),
        },
      );

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to get signed URL');
        return null;
      }

      final signedUrl = response.data!['signed_url'] as String?;
      _logger.info('Signed URL generated successfully');
      return signedUrl;
    } catch (e) {
      _logger.severe('Error getting signed URL: $e');
      return null;
    }
  }

  /// Check if storage service is available through Go backend
  /// 
  /// Returns true if the storage service is healthy
  Future<bool> checkStorageHealth() async {
    try {
      _logger.info('Checking storage health through Go backend');

      final response = await _apiClient.getApi<Map<String, dynamic>>(
        '/storage/health',
      );

      final isHealthy = response.isSuccess;
      _logger.info('Storage health check result: $isHealthy');
      return isHealthy;
    } catch (e) {
      _logger.severe('Error checking storage health: $e');
      return false;
    }
  }

  /// Get storage usage statistics through Go backend
  /// 
  /// Returns storage usage information
  Future<Map<String, dynamic>?> getStorageStats() async {
    try {
      _logger.info('Getting storage stats through Go backend');

      final response = await _apiClient.getApi<Map<String, dynamic>>(
        '/storage/stats',
      );

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to get storage stats');
        return null;
      }

      _logger.info('Storage stats retrieved successfully');
      return response.data!;
    } catch (e) {
      _logger.severe('Error getting storage stats: $e');
      return null;
    }
  }

  /// Upload multiple product images through Go backend
  /// 
  /// [files]: List of File objects to upload
  /// [productId]: Product ID for organized naming
  /// 
  /// Returns list of public URLs of uploaded images
  Future<List<String>> uploadMultipleProductImages(
    List<File> files,
    String productId,
  ) async {
    final urls = <String>[];
    
    for (final file in files) {
      try {
        final url = await uploadProductImage(
          filePath: file.path,
          productId: productId,
        );
        urls.add(url);
      } catch (e) {
        _logger.warning('Failed to upload image ${file.path}: $e');
        // Continue with other images
      }
    }
    
    return urls;
  }

  /// Check if product images bucket exists - deprecated
  /// 
  /// Always returns true since Go backend handles bucket management
  Future<bool> checkProductImagesBucketExists() async {
    return true; // Go backend handles bucket management
  }
}
