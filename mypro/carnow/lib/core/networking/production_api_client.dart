// Production API Client for CarNow
// عميل API الإنتاج لتطبيق CarNow
// 
// This file implements a production-ready API client with:
// - Automatic Authorization header injection
// - Retry logic with exponential backoff
// - Request/response logging for debugging
// - Comprehensive error handling and mapping
// - Forever Plan Architecture compliance: Flutter UI Only → Go API → Supabase Data

import 'dart:async';

import 'dart:developer' as developer;
import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


import '../auth/simple_supabase_auth_provider.dart';

/// Production-ready API client with comprehensive error handling
/// عميل API جاهز للإنتاج مع معالجة شاملة للأخطاء
class ProductionApiClient {
  final Dio _dio;
  final Ref _ref;
  late final String _baseUrl;
  
  // Retry configuration
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(milliseconds: 500);
  static const Duration _maxDelay = Duration(seconds: 10);
  static const Duration _requestTimeout = Duration(seconds: 30);
  
  ProductionApiClient({
    required Ref ref,
    String? baseUrl,
    Duration? timeout,
  }) : _ref = ref, _dio = Dio() {
    _baseUrl = baseUrl ?? 'https://backend-go-8klm.onrender.com';
    _setupDio(timeout ?? _requestTimeout);
    _setupInterceptors();
  }

  /// Setup Dio configuration
  /// إعداد تكوين Dio
  void _setupDio(Duration timeout) {
    _dio.options = BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: timeout,
      receiveTimeout: timeout,
      sendTimeout: timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'CarNow-Flutter/1.0.0',
      },
      validateStatus: (status) {
        // Accept all status codes to handle them manually
        return status != null && status < 600;
      },
    );
  }

  /// Setup request/response interceptors
  /// إعداد مُعترضات الطلبات والاستجابات
  void _setupInterceptors() {
    // Request interceptor for authentication and logging
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          _injectAuthHeader(options);
          _logRequest(options);
          handler.next(options);
        },
        onResponse: (response, handler) {
          _logResponse(response);
          handler.next(response);
        },
        onError: (error, handler) {
          _logError(error);
          handler.next(error);
        },
      ),
    );
  }

  /// Inject Authorization header if user is authenticated
  /// حقن رأس التفويض إذا كان المستخدم مُصدق
  void _injectAuthHeader(RequestOptions options) {
    try {
      final authState = _ref.read(simpleSupabaseAuthProvider);
      final accessToken = authState is SimpleAuthStateAuthenticated ? authState.token : null;
      if (accessToken != null && accessToken.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $accessToken';
        developer.log(
          'Injected auth header for ${options.method} ${options.path}',
          name: 'ProductionApiClient',
        );
      }
    } catch (e) {
      developer.log(
        'Failed to inject auth header: $e',
        name: 'ProductionApiClient',
        level: 900, // Warning level
      );
    }
  }

  /// Log outgoing requests
  /// تسجيل الطلبات الصادرة
  void _logRequest(RequestOptions options) {
    developer.log(
      '→ ${options.method} ${options.uri}',
      name: 'ProductionApiClient.Request',
    );
    
    if (options.data != null) {
      developer.log(
        '→ Body: ${_sanitizeLogData(options.data)}',
        name: 'ProductionApiClient.Request',
      );
    }
    
    if (options.queryParameters.isNotEmpty) {
      developer.log(
        '→ Query: ${options.queryParameters}',
        name: 'ProductionApiClient.Request',
      );
    }
  }

  /// Log incoming responses
  /// تسجيل الاستجابات الواردة
  void _logResponse(Response response) {
    final statusCode = response.statusCode ?? 0;
    final method = response.requestOptions.method;
    final path = response.requestOptions.path;
    
    developer.log(
      '← $method $path ($statusCode)',
      name: 'ProductionApiClient.Response',
    );
    
    if (response.data != null) {
      developer.log(
        '← Body: ${_sanitizeLogData(response.data)}',
        name: 'ProductionApiClient.Response',
      );
    }
  }

  /// Log errors
  /// تسجيل الأخطاء
  void _logError(DioException error) {
    final statusCode = error.response?.statusCode ?? 0;
    final method = error.requestOptions.method;
    final path = error.requestOptions.path;
    
    developer.log(
      '✗ $method $path ($statusCode) - ${error.type}',
      name: 'ProductionApiClient.Error',
      level: 1000, // Error level
    );
    
    if (error.response?.data != null) {
      developer.log(
        '✗ Error Body: ${_sanitizeLogData(error.response!.data)}',
        name: 'ProductionApiClient.Error',
        level: 1000,
      );
    }
  }

  /// Sanitize sensitive data from logs
  /// تنظيف البيانات الحساسة من السجلات
  String _sanitizeLogData(dynamic data) {
    if (data == null) return 'null';
    
    String dataStr = data.toString();
    
    // Remove sensitive information
    dataStr = dataStr.replaceAll(RegExp(r'"password"\s*:\s*"[^"]*"'), '"password":"***"');
    dataStr = dataStr.replaceAll(RegExp(r'"token"\s*:\s*"[^"]*"'), '"token":"***"');
    dataStr = dataStr.replaceAll(RegExp(r'"access_token"\s*:\s*"[^"]*"'), '"access_token":"***"');
    dataStr = dataStr.replaceAll(RegExp(r'"refresh_token"\s*:\s*"[^"]*"'), '"refresh_token":"***"');
    
    // Truncate if too long
    if (dataStr.length > 500) {
      dataStr = '${dataStr.substring(0, 500)}...';
    }
    
    return dataStr;
  }

  /// Execute request with retry logic and error handling
  /// تنفيذ الطلب مع منطق إعادة المحاولة ومعالجة الأخطاء
  Future<T> _executeWithRetry<T>(
    Future<Response> Function() request,
    T Function(Response response) parser,
    String operationName,
  ) async {
    int attempt = 0;
    DioException? lastError;

    while (attempt < _maxRetries) {
      try {
        final response = await request();
        
        // Handle successful responses
        if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
          return parser(response);
        }
        
        // Handle authentication errors
        if (response.statusCode == 401) {
          await _handleUnauthorized();
          throw ApiException(
            type: ApiErrorType.unauthorized,
            message: 'Authentication required',
            statusCode: 401,
          );
        }
        
        // Handle other HTTP errors
        throw ApiException(
          type: _mapStatusCodeToErrorType(response.statusCode ?? 0),
          message: _extractErrorMessage(response),
          statusCode: response.statusCode ?? 0,
          data: response.data,
        );
        
      } on DioException catch (e) {
        lastError = e;
        
        // Don't retry certain types of errors
        if (!_shouldRetry(e, attempt)) {
          throw _mapDioException(e, operationName);
        }
        
        attempt++;
        if (attempt < _maxRetries) {
          final delay = _calculateDelay(attempt);
          developer.log(
            'Retrying $operationName (attempt $attempt) after ${delay.inMilliseconds}ms',
            name: 'ProductionApiClient',
          );
          await Future.delayed(delay);
        }
      } catch (e) {
        // Handle non-Dio exceptions
        throw ApiException(
          type: ApiErrorType.unknown,
          message: 'Unexpected error: $e',
          statusCode: 0,
        );
      }
    }

    // All retries failed
    throw _mapDioException(lastError!, operationName);
  }

  /// Determine if request should be retried
  /// تحديد ما إذا كان يجب إعادة محاولة الطلب
  bool _shouldRetry(DioException error, int attempt) {
    if (attempt >= _maxRetries) return false;
    
    // Don't retry client errors (4xx) except for specific cases
    final statusCode = error.response?.statusCode ?? 0;
    if (statusCode >= 400 && statusCode < 500) {
      // Only retry 408 (timeout), 429 (rate limit)
      return statusCode == 408 || statusCode == 429;
    }
    
    // Retry server errors (5xx) and network errors
    return error.type == DioExceptionType.connectionTimeout ||
           error.type == DioExceptionType.receiveTimeout ||
           error.type == DioExceptionType.sendTimeout ||
           error.type == DioExceptionType.connectionError ||
           statusCode >= 500;
  }

  /// Calculate exponential backoff delay
  /// حساب تأخير الانتظار التصاعدي
  Duration _calculateDelay(int attempt) {
    final delay = _baseDelay * pow(2, attempt - 1);
    final jitter = Duration(milliseconds: Random().nextInt(100));
    final totalDelay = delay + jitter;
    
    return totalDelay > _maxDelay ? _maxDelay : totalDelay;
  }

  /// Handle unauthorized responses
  /// معالجة الاستجابات غير المصرح بها
  Future<void> _handleUnauthorized() async {
    try {
      developer.log(
        'Received 401 Unauthorized - signing out user',
        name: 'ProductionApiClient',
        level: 900,
      );
      
      final authNotifier = _ref.read(simpleSupabaseAuthProvider.notifier);
      await authNotifier.signOut();
    } catch (e) {
      developer.log(
        'Failed to handle unauthorized response: $e',
        name: 'ProductionApiClient',
        level: 1000,
      );
    }
  }

  /// Map HTTP status codes to error types
  /// ربط رموز حالة HTTP بأنواع الأخطاء
  ApiErrorType _mapStatusCodeToErrorType(int statusCode) {
    switch (statusCode) {
      case 400:
        return ApiErrorType.badRequest;
      case 401:
        return ApiErrorType.unauthorized;
      case 403:
        return ApiErrorType.forbidden;
      case 404:
        return ApiErrorType.notFound;
      case 408:
        return ApiErrorType.timeout;
      case 409:
        return ApiErrorType.conflict;
      case 422:
        return ApiErrorType.validation;
      case 429:
        return ApiErrorType.rateLimited;
      case 500:
        return ApiErrorType.serverError;
      case 502:
      case 503:
      case 504:
        return ApiErrorType.serviceUnavailable;
      default:
        return ApiErrorType.unknown;
    }
  }

  /// Extract error message from response
  /// استخراج رسالة الخطأ من الاستجابة
  String _extractErrorMessage(Response response) {
    try {
      final data = response.data;
      if (data is Map<String, dynamic>) {
        return data['message'] ?? 
               data['error'] ?? 
               data['detail'] ?? 
               'Request failed with status ${response.statusCode}';
      }
      return data?.toString() ?? 'Request failed with status ${response.statusCode}';
    } catch (e) {
      return 'Request failed with status ${response.statusCode}';
    }
  }

  /// Map DioException to ApiException
  /// ربط DioException بـ ApiException
  ApiException _mapDioException(DioException error, String operationName) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException(
          type: ApiErrorType.timeout,
          message: 'Request timeout for $operationName',
          statusCode: 408,
        );
        
      case DioExceptionType.connectionError:
        return ApiException(
          type: ApiErrorType.networkError,
          message: 'Network connection failed for $operationName',
          statusCode: 0,
        );
        
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode ?? 0;
        return ApiException(
          type: _mapStatusCodeToErrorType(statusCode),
          message: _extractErrorMessage(error.response!),
          statusCode: statusCode,
          data: error.response?.data,
        );
        
      case DioExceptionType.cancel:
        return ApiException(
          type: ApiErrorType.cancelled,
          message: 'Request was cancelled for $operationName',
          statusCode: 0,
        );
        
      default:
        return ApiException(
          type: ApiErrorType.unknown,
          message: 'Unknown error occurred for $operationName: ${error.message}',
          statusCode: 0,
        );
    }
  }

  // Public API methods

  /// GET request with retry logic
  /// طلب GET مع منطق إعادة المحاولة
  Future<T> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    required T Function(Map<String, dynamic> data) fromJson,
  }) async {
    return _executeWithRetry(
      () => _dio.get(
        path,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      ),
      (response) => fromJson(response.data as Map<String, dynamic>),
      'GET $path',
    );
  }

  /// POST request with retry logic
  /// طلب POST مع منطق إعادة المحاولة
  Future<T> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    required T Function(Map<String, dynamic> data) fromJson,
  }) async {
    return _executeWithRetry(
      () => _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      ),
      (response) => fromJson(response.data as Map<String, dynamic>),
      'POST $path',
    );
  }

  /// PUT request with retry logic
  /// طلب PUT مع منطق إعادة المحاولة
  Future<T> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    required T Function(Map<String, dynamic> data) fromJson,
  }) async {
    return _executeWithRetry(
      () => _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      ),
      (response) => fromJson(response.data as Map<String, dynamic>),
      'PUT $path',
    );
  }

  /// DELETE request with retry logic
  /// طلب DELETE مع منطق إعادة المحاولة
  Future<T> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    required T Function(Map<String, dynamic> data) fromJson,
  }) async {
    return _executeWithRetry(
      () => _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      ),
      (response) => fromJson(response.data as Map<String, dynamic>),
      'DELETE $path',
    );
  }

  /// Upload file with progress tracking
  /// رفع ملف مع تتبع التقدم
  Future<T> uploadFile<T>(
    String path,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
    Map<String, dynamic>? headers,
    void Function(int sent, int total)? onProgress,
    required T Function(Map<String, dynamic> data) fromJson,
  }) async {
    final fileName = file.path.split('/').last;
    final formData = FormData.fromMap({
      fieldName: await MultipartFile.fromFile(
        file.path,
        filename: fileName,
      ),
      ...?additionalData,
    });

    return _executeWithRetry(
      () => _dio.post(
        path,
        data: formData,
        options: Options(headers: headers),
        onSendProgress: onProgress,
      ),
      (response) => fromJson(response.data as Map<String, dynamic>),
      'UPLOAD $path',
    );
  }

  /// Cancel all pending requests
  /// إلغاء جميع الطلبات المعلقة
  void cancelRequests() {
    _dio.close();
    developer.log(
      'Cancelled all pending requests',
      name: 'ProductionApiClient',
    );
  }

  /// Dispose resources
  /// تحرير الموارد
  void dispose() {
    _dio.close();
    developer.log(
      'ProductionApiClient disposed',
      name: 'ProductionApiClient',
    );
  }
}

/// API error types for comprehensive error handling
/// أنواع أخطاء API للمعالجة الشاملة للأخطاء
enum ApiErrorType {
  networkError,
  timeout,
  unauthorized,
  forbidden,
  notFound,
  badRequest,
  conflict,
  validation,
  rateLimited,
  serverError,
  serviceUnavailable,
  cancelled,
  unknown,
}

/// API exception with detailed error information
/// استثناء API مع معلومات مفصلة عن الخطأ
class ApiException implements Exception {
  final ApiErrorType type;
  final String message;
  final int statusCode;
  final dynamic data;

  const ApiException({
    required this.type,
    required this.message,
    required this.statusCode,
    this.data,
  });

  @override
  String toString() {
    return 'ApiException(type: $type, message: $message, statusCode: $statusCode)';
  }

  /// Get localized error message in Arabic
  /// الحصول على رسالة خطأ محلية بالعربية
  String get localizedMessage {
    switch (type) {
      case ApiErrorType.networkError:
        return 'خطأ في الاتصال بالشبكة';
      case ApiErrorType.timeout:
        return 'انتهت مهلة الاتصال';
      case ApiErrorType.unauthorized:
        return 'يجب تسجيل الدخول أولاً';
      case ApiErrorType.forbidden:
        return 'غير مصرح لك بالوصول';
      case ApiErrorType.notFound:
        return 'المورد المطلوب غير موجود';
      case ApiErrorType.badRequest:
        return 'طلب غير صحيح';
      case ApiErrorType.conflict:
        return 'تعارض في البيانات';
      case ApiErrorType.validation:
        return 'خطأ في التحقق من البيانات';
      case ApiErrorType.rateLimited:
        return 'تم تجاوز حد الطلبات المسموح';
      case ApiErrorType.serverError:
        return 'خطأ في الخادم';
      case ApiErrorType.serviceUnavailable:
        return 'الخدمة غير متاحة حالياً';
      case ApiErrorType.cancelled:
        return 'تم إلغاء الطلب';
      case ApiErrorType.unknown:
        return 'حدث خطأ غير متوقع';
    }
  }

  /// Check if error is retryable
  /// التحقق من إمكانية إعادة المحاولة
  bool get isRetryable {
    switch (type) {
      case ApiErrorType.networkError:
      case ApiErrorType.timeout:
      case ApiErrorType.serverError:
      case ApiErrorType.serviceUnavailable:
        return true;
      default:
        return false;
    }
  }
}

/// Riverpod provider for ProductionApiClient
/// مزود Riverpod لـ ProductionApiClient
final productionApiClientProvider = Provider<ProductionApiClient>((ref) {
  final client = ProductionApiClient(ref: ref);
  
  // Dispose when provider is disposed
  ref.onDispose(() {
    client.dispose();
  });
  
  return client;
});
