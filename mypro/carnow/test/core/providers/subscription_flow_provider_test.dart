import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../lib/core/providers/subscription_flow_provider.dart';
import '../../../lib/core/services/subscription_service.dart';
import '../../../lib/core/models/subscription_request.dart';
import '../../../lib/core/models/subscription_response.dart';
import '../../../lib/core/models/subscription_error.dart';
import '../../../lib/core/auth/simple_supabase_auth_provider.dart';
import '../../../lib/core/models/user_model.dart';
import '../../../lib/core/networking/simple_api_client.dart';

// Simple mock implementation
class MockSubscriptionService implements SubscriptionService {
  SubscriptionResult<SubscriptionResponse>? _submitResult;
  SubscriptionResult<List<SubscriptionResponse>>? _getResult;

  void setSubmitResult(SubscriptionResult<SubscriptionResponse> result) {
    _submitResult = result;
  }

  void setGetResult(SubscriptionResult<List<SubscriptionResponse>> result) {
    _getResult = result;
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> submitSubscriptionRequest(
    SubscriptionRequest request,
  ) async {
    if (_submitResult != null) {
      return _submitResult!;
    }
    return SubscriptionResult.success(
      SubscriptionResponse(
        id: 'test-sub-123',
        status: 'success',
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        message: 'Test subscription created',
      ),
    );
  }

  @override
  Future<SubscriptionResult<List<SubscriptionResponse>>> getUserSubscriptions(
    String userId,
  ) async {
    if (_getResult != null) {
      return _getResult!;
    }
    return SubscriptionResult.success([]);
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> getSubscriptionStatus(
    String subscriptionId,
  ) async {
    return SubscriptionResult.success(
      SubscriptionResponse(
        id: subscriptionId,
        status: 'active',
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        message: 'Test subscription status',
      ),
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> updateSubscription(
    String subscriptionId,
    SubscriptionRequest request,
  ) async {
    return SubscriptionResult.success(
      SubscriptionResponse(
        id: subscriptionId,
        status: 'updated',
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        message: 'Test subscription updated',
      ),
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> cancelSubscription(
    String subscriptionId,
  ) async {
    return SubscriptionResult.success(
      SubscriptionResponse(
        id: subscriptionId,
        status: 'cancelled',
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        message: 'Test subscription cancelled',
      ),
    );
  }
}

void main() {
  group('CoreSubscriptionFlowProvider Tests - Fixed', () {
    late MockSubscriptionService mockSubscriptionService;
    late ProviderContainer container;

    setUp(() {
      mockSubscriptionService = MockSubscriptionService();

      container = ProviderContainer(
        overrides: [
          subscriptionServiceProvider.overrideWithValue(
            mockSubscriptionService,
          ),
          simpleSupabaseAuthProvider.overrideWith(
            (ref) => SimpleSupabaseAuthProvider(
              apiClient: ref.read(simpleApiClientProvider),
            )..state = SimpleAuthStateAuthenticated(
              user: UserModel(
                id: '550e8400-e29b-41d4-a716-446655440000',
                authId: 'auth-123',
                email: '<EMAIL>',
                name: 'Test User',
                phone: '+966501234567',
                isActive: true,
                createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
                updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
              ),
              token: 'test-token-123',
            ),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('should initialize with correct initial state', () {
      final provider = container.read(coreSubscriptionFlowProviderProvider);

      expect(provider.status, CoreSubscriptionFlowStatus.initial);
      expect(provider.formData.storeName, '');
      expect(provider.formData.isValid, false);
      expect(provider.progress, 0.0);
      expect(provider.hasError, false);
      expect(provider.isLoading, false);
      expect(provider.canSubmit, false);
    });

    test('should initialize flow with user context', () async {
      final notifier = container.read(
        coreSubscriptionFlowProviderProvider.notifier,
      );

      await notifier.initializeFlow();

      final state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.status, CoreSubscriptionFlowStatus.editing);
      expect(state.formData.userId, '550e8400-e29b-41d4-a716-446655440000');
      expect(state.progress, 0.1);
    });

    test('should update individual form fields', () async {
      final notifier = container.read(
        coreSubscriptionFlowProviderProvider.notifier,
      );
      await notifier.initializeFlow();

      // Test updating store name
      await notifier.updateFormField(storeName: 'Test Store Name');
      var state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.formData.storeName, 'Test Store Name');

      // Test updating phone
      await notifier.updateFormField(phone: '+966501234567');
      state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.formData.phone, '+966501234567');

      // Test updating price
      await notifier.updateFormField(price: 299.99);
      state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.formData.price, 299.99);
    });

    test('should validate form correctly', () async {
      final notifier = container.read(
        coreSubscriptionFlowProviderProvider.notifier,
      );
      await notifier.initializeFlow();

      // Test with invalid data
      await notifier.updateFormField(storeName: 'A'); // Too short
      var state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.formData.isValid, false);

      // Test with valid data
      await notifier.updateFormField(storeName: 'Valid Store Name');
      await notifier.updateFormField(phone: '+966501234567');
      await notifier.updateFormField(city: 'Riyadh');
      await notifier.updateFormField(
        address: 'Valid address with more than 10 characters',
      );
      await notifier.updateFormField(
        description: 'Valid description with more than 10 characters',
      );
      await notifier.updateFormField(planType: 'basic');
      await notifier.updateFormField(price: 200.0);

      state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.formData.isValid, true);
      expect(state.canSubmit, true);
    });

    test('should submit subscription successfully', () async {
      final mockResponse = SubscriptionResponse(
        id: 'sub-123',
        status: 'success',
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        message: 'Subscription created successfully',
      );

      mockSubscriptionService.setSubmitResult(
        SubscriptionResult.success(mockResponse),
      );

      final notifier = container.read(
        coreSubscriptionFlowProviderProvider.notifier,
      );
      await notifier.initializeFlow();

      // Set up valid form data
      await notifier.updateFormField(storeName: 'Valid Store Name');
      await notifier.updateFormField(phone: '+966501234567');
      await notifier.updateFormField(city: 'Riyadh');
      await notifier.updateFormField(
        address: 'Valid address with more than 10 characters',
      );
      await notifier.updateFormField(
        description: 'Valid description with more than 10 characters',
      );
      await notifier.updateFormField(planType: 'basic');
      await notifier.updateFormField(price: 200.0);

      await notifier.submitSubscription();

      final finalState = container.read(coreSubscriptionFlowProviderProvider);
      expect(finalState.status, CoreSubscriptionFlowStatus.completed);
      expect(finalState.response?.id, 'sub-123');
      expect(finalState.progress, 1.0);
    });

    test('should handle submission failure', () async {
      const mockError = SubscriptionError.networkError(
        message: 'Network connection failed',
        code: 'NETWORK_ERROR',
      );

      mockSubscriptionService.setSubmitResult(
        const SubscriptionResult.failure(mockError),
      );

      final notifier = container.read(
        coreSubscriptionFlowProviderProvider.notifier,
      );
      await notifier.initializeFlow();

      // Set up valid form data
      await notifier.updateFormField(storeName: 'Valid Store Name');
      await notifier.updateFormField(phone: '+966501234567');
      await notifier.updateFormField(city: 'Riyadh');
      await notifier.updateFormField(
        address: 'Valid address with more than 10 characters',
      );
      await notifier.updateFormField(
        description: 'Valid description with more than 10 characters',
      );
      await notifier.updateFormField(planType: 'basic');
      await notifier.updateFormField(price: 200.0);

      await notifier.submitSubscription();

      final state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.status, CoreSubscriptionFlowStatus.failed);
      expect(state.error, isA<SubscriptionError>());
      expect(state.hasError, true);
    });

    test('should cancel flow correctly', () async {
      final notifier = container.read(
        coreSubscriptionFlowProviderProvider.notifier,
      );

      await notifier.initializeFlow();
      await notifier.cancelFlow();

      final state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.status, CoreSubscriptionFlowStatus.cancelled);
      expect(state.progress, 0.0);
      expect(state.error, isNull);
    });

    test('should reset flow correctly', () async {
      final notifier = container.read(
        coreSubscriptionFlowProviderProvider.notifier,
      );

      await notifier.initializeFlow();
      await notifier.updateFormField(storeName: 'Test Store');
      await notifier.resetFlow();

      final state = container.read(coreSubscriptionFlowProviderProvider);
      expect(state.status, CoreSubscriptionFlowStatus.initial);
      expect(state.formData.storeName, '');
      expect(state.progress, 0.0);
      expect(state.error, isNull);
    });

    group('Provider Tests', () {
      test('should provide correct form validation state', () async {
        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();
        expect(container.read(isCoreSubscriptionFormValidProvider), false);

        // Set up valid form
        await notifier.updateFormField(storeName: 'Valid Store');
        await notifier.updateFormField(phone: '+966501234567');
        await notifier.updateFormField(city: 'Riyadh');
        await notifier.updateFormField(
          address: 'Valid address with more than 10 characters',
        );
        await notifier.updateFormField(
          description: 'Valid description with more than 10 characters',
        );
        await notifier.updateFormField(planType: 'basic');
        await notifier.updateFormField(price: 200.0);

        expect(container.read(isCoreSubscriptionFormValidProvider), true);
      });

      test('should provide correct loading state', () async {
        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        expect(container.read(isCoreSubscriptionLoadingProvider), false);

        await notifier.initializeFlow();
        expect(container.read(isCoreSubscriptionLoadingProvider), false);
      });

      test('should provide correct error states', () async {
        const mockError = SubscriptionError.validationError(
          message: 'Validation failed',
          fieldErrors: {'storeName': 'اسم المتجر مطلوب'},
        );

        mockSubscriptionService.setSubmitResult(
          const SubscriptionResult.failure(mockError),
        );

        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();

        // Set up valid form
        await notifier.updateFormField(storeName: 'Valid Store');
        await notifier.updateFormField(phone: '+966501234567');
        await notifier.updateFormField(city: 'Riyadh');
        await notifier.updateFormField(
          address: 'Valid address with more than 10 characters',
        );
        await notifier.updateFormField(
          description: 'Valid description with more than 10 characters',
        );
        await notifier.updateFormField(planType: 'basic');
        await notifier.updateFormField(price: 200.0);

        await notifier.submitSubscription();

        expect(
          container.read(coreSubscriptionErrorProvider),
          isA<SubscriptionError>(),
        );
        expect(container.read(coreSubscriptionErrorArabicProvider), isNotNull);
      });
    });

    group('Error Handling Tests', () {
      test('should handle network errors correctly', () async {
        const networkError = SubscriptionError.networkError(
          message: 'Network connection failed',
          code: 'NETWORK_ERROR',
        );

        mockSubscriptionService.setSubmitResult(
          const SubscriptionResult.failure(networkError),
        );

        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();

        // Set up valid form
        await notifier.updateFormField(storeName: 'Valid Store');
        await notifier.updateFormField(phone: '+966501234567');
        await notifier.updateFormField(city: 'Riyadh');
        await notifier.updateFormField(
          address: 'Valid address with more than 10 characters',
        );
        await notifier.updateFormField(
          description: 'Valid description with more than 10 characters',
        );
        await notifier.updateFormField(planType: 'basic');
        await notifier.updateFormField(price: 200.0);

        await notifier.submitSubscription();

        final state = container.read(coreSubscriptionFlowProviderProvider);
        expect(state.error, isA<NetworkError>());
        expect(state.error?.userFriendlyMessageArabic, contains('اتصال'));
      });

      test('should handle validation errors correctly', () async {
        const validationError = SubscriptionError.validationError(
          message: 'Validation failed',
          fieldErrors: {
            'storeName': 'اسم المتجر مطلوب',
            'phone': 'رقم الهاتف غير صحيح',
          },
        );

        mockSubscriptionService.setSubmitResult(
          const SubscriptionResult.failure(validationError),
        );

        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();

        // Set up valid form
        await notifier.updateFormField(storeName: 'Valid Store');
        await notifier.updateFormField(phone: '+966501234567');
        await notifier.updateFormField(city: 'Riyadh');
        await notifier.updateFormField(
          address: 'Valid address with more than 10 characters',
        );
        await notifier.updateFormField(
          description: 'Valid description with more than 10 characters',
        );
        await notifier.updateFormField(planType: 'basic');
        await notifier.updateFormField(price: 200.0);

        await notifier.submitSubscription();

        final state = container.read(coreSubscriptionFlowProviderProvider);
        expect(state.error, isA<ValidationError>());
        expect(state.error?.userFriendlyMessageArabic, contains('بيانات'));
      });
    });
  });
}
