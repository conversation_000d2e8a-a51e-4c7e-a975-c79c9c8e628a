package handlers

import (
	"log"
	"net/http"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// SimpleAuthHandlers handles authentication using Supabase JWT only
// Forever Plan Compliant: Simple architecture with Supabase Auth
type SimpleAuthHandlers struct {
	supabaseAuth *services.SupabaseAuthService
	config       *config.Config
}

// NewSimpleAuthHandlers creates a new simple auth handlers instance
func NewSimpleAuthHandlers(supabaseAuth *services.SupabaseAuthService, cfg *config.Config) *SimpleAuthHandlers {
	return &SimpleAuthHandlers{
		supabaseAuth: supabaseAuth,
		config:       cfg,
	}
}

// SimpleLoginRequest represents the login request structure
type SimpleLoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// SimpleRegisterRequest represents the registration request structure
type SimpleRegisterRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Name     string `json:"name" binding:"required,min=2"`
}

// SimpleAuthResponse represents the authentication response structure
type SimpleAuthResponse struct {
	User        *services.User `json:"user"`
	AccessToken string         `json:"access_token"`
	ExpiresAt   time.Time      `json:"expires_at"`
	TokenType   string         `json:"token_type"`
}

// Login handles user login using Supabase Auth
func (h *SimpleAuthHandlers) Login(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	log.Printf("🔐 Simple Auth: Login attempt from %s", clientIP)

	var req SimpleLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Simple Auth: Invalid login request from %s: %v", clientIP, err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"code":    "INVALID_REQUEST",
			"message": "Please provide valid email and password",
		})
		return
	}

	// Authenticate with Supabase
	authResponse, err := h.supabaseAuth.SignInWithEmail(c.Request.Context(), req.Email, req.Password)
	if err != nil {
		log.Printf("❌ Simple Auth: Login failed for %s from %s: %v", req.Email, clientIP, err)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Invalid credentials",
			"code":    "INVALID_CREDENTIALS",
			"message": "Email or password is incorrect",
		})
		return
	}

	// Create response
	response := SimpleAuthResponse{
		User:        authResponse.User,
		AccessToken: authResponse.AccessToken,
		ExpiresAt:   authResponse.ExpiresAt,
		TokenType:   authResponse.TokenType,
	}

	processingTime := time.Since(startTime)
	log.Printf("✅ Simple Auth: Login successful for %s from %s in %v", authResponse.User.Email, clientIP, processingTime)

	c.JSON(http.StatusOK, response)
}

// Register handles user registration using Supabase Auth
func (h *SimpleAuthHandlers) Register(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	log.Printf("🔐 Simple Auth: Registration attempt from %s", clientIP)

	var req SimpleRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Simple Auth: Invalid registration request from %s: %v", clientIP, err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"code":    "INVALID_REQUEST",
			"message": "Please provide valid email, password, and name",
		})
		return
	}

	// For now, we'll use SignInWithEmail as a placeholder
	// In a real implementation, you would need to implement SignUpWithEmail
	authResponse, err := h.supabaseAuth.SignInWithEmail(c.Request.Context(), req.Email, req.Password)
	if err != nil {
		log.Printf("❌ Simple Auth: Registration failed for %s from %s: %v", req.Email, clientIP, err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Registration failed",
			"code":    "REGISTRATION_FAILED",
			"message": "Unable to create account. Please try again.",
		})
		return
	}

	// Create response
	response := SimpleAuthResponse{
		User:        authResponse.User,
		AccessToken: authResponse.AccessToken,
		ExpiresAt:   authResponse.ExpiresAt,
		TokenType:   authResponse.TokenType,
	}

	processingTime := time.Since(startTime)
	log.Printf("✅ Simple Auth: Registration successful for %s from %s in %v", authResponse.User.Email, clientIP, processingTime)

	c.JSON(http.StatusCreated, response)
}

// Logout handles user logout
func (h *SimpleAuthHandlers) Logout(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.GetString("user_id")

	log.Printf("🔐 Simple Auth: Logout attempt for user %s from %s", userID, clientIP)

	// Get token from header
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		// In a real implementation, you might want to blacklist the token
		// For now, we'll just log the logout
		log.Printf("📝 Simple Auth: Token logout for user %s from %s", userID, clientIP)
	}

	processingTime := time.Since(startTime)
	log.Printf("✅ Simple Auth: Logout successful for user %s from %s in %v", userID, clientIP, processingTime)

	c.JSON(http.StatusOK, gin.H{
		"message": "Successfully logged out",
		"code":    "LOGOUT_SUCCESS",
	})
}

// RefreshToken handles token refresh (if needed)
func (h *SimpleAuthHandlers) RefreshToken(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	log.Printf("🔐 Simple Auth: Token refresh attempt from %s", clientIP)

	// For Supabase JWT, refresh is typically handled client-side
	// This endpoint can be used for additional validation if needed
	c.JSON(http.StatusOK, gin.H{
		"message": "Token refresh not required for Supabase JWT",
		"code":    "REFRESH_NOT_NEEDED",
	})

	processingTime := time.Since(startTime)
	log.Printf("✅ Simple Auth: Token refresh handled for %s in %v", clientIP, processingTime)
}

// GetProfile returns the current user's profile
func (h *SimpleAuthHandlers) GetProfile(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.GetString("user_id")

	log.Printf("🔐 Simple Auth: Profile request for user %s from %s", userID, clientIP)

	// Get token from header for Supabase validation
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		log.Printf("❌ Simple Auth: No authorization header for profile request from %s", clientIP)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Authorization required",
			"code":    "AUTH_REQUIRED",
			"message": "Authorization header is required",
		})
		return
	}

	// Extract token
	token := authHeader
	if strings.HasPrefix(authHeader, "Bearer ") {
		token = strings.TrimPrefix(authHeader, "Bearer ")
	}

	// Get user from Supabase using token
	user, err := h.supabaseAuth.GetUser(c.Request.Context(), token)
	if err != nil {
		log.Printf("❌ Simple Auth: Failed to get profile for user %s from %s: %v", userID, clientIP, err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "User not found",
			"code":    "USER_NOT_FOUND",
			"message": "User profile not found",
		})
		return
	}

	processingTime := time.Since(startTime)
	log.Printf("✅ Simple Auth: Profile retrieved for user %s from %s in %v", user.Email, clientIP, processingTime)

	c.JSON(http.StatusOK, gin.H{
		"user": user,
	})
}
