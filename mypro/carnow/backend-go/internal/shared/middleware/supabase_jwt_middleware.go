package middleware

import (
	"log"
	"net/http"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// SupabaseJWTMiddleware validates Supabase JWT tokens only
// Forever Plan Compliant: Simple architecture with Supabase Auth
func SupabaseJWTMiddleware(cfg *config.Config, supabaseAuth *services.SupabaseAuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()
		clientIP := c.ClientIP()

		log.Printf("🔍 Supabase JWT Middleware: Processing request from %s", clientIP)

		// Extract Bearer token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			log.Printf("❌ Supabase JWT Middleware: No authorization header from %s", clientIP)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Authorization header required",
				"code":    "AUTH_HEADER_MISSING",
				"message": "Please provide a valid Supabase token",
			})
			c.Abort()
			return
		}

		// Validate Bearer format
		if !strings.HasPrefix(authHeader, "Bearer ") {
			log.Printf("❌ Supabase JWT Middleware: Invalid Bearer format from %s", clientIP)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid authorization header format",
				"code":    "AUTH_HEADER_INVALID",
				"message": "Authorization header must be: Bearer <token>",
			})
			c.Abort()
			return
		}

		// Extract token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			log.Printf("❌ Supabase JWT Middleware: Empty token from %s", clientIP)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Empty token provided",
				"code":    "TOKEN_EMPTY",
				"message": "Token cannot be empty",
			})
			c.Abort()
			return
		}

		// Validate token with Supabase
		user, err := supabaseAuth.GetUser(c.Request.Context(), token)
		if err != nil {
			log.Printf("❌ Supabase JWT Middleware: Token validation failed for %s: %v", clientIP, err)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid or expired token",
				"code":    "TOKEN_VALIDATION_FAILED",
				"message": "Please login again",
			})
			c.Abort()
			return
		}

		// Set user context (Forever Plan: Real data from Supabase)
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("auth_source", "supabase")
		c.Set("auth_timestamp", time.Now())
		c.Set("client_ip", clientIP)

		processingTime := time.Since(startTime)
		log.Printf("✅ Supabase JWT Middleware: Token validated for user %s (%s) in %v",
			user.Email, user.ID, processingTime)

		c.Next()
	}
}

// OptionalSupabaseJWTMiddleware allows both authenticated and non-authenticated requests
func OptionalSupabaseJWTMiddleware(cfg *config.Config, supabaseAuth *services.SupabaseAuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")

		// If no auth header, continue without setting user context
		if authHeader == "" {
			c.Next()
			return
		}

		// If auth header exists, try to validate it
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.Next()
			return
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.Next()
			return
		}

		// Try to validate Supabase token
		user, err := supabaseAuth.GetUser(c.Request.Context(), token)
		if err != nil {
			log.Printf("⚠️ Supabase JWT Middleware (Optional): Token validation failed, continuing without auth: %v", err)
			c.Next()
			return
		}

		// Valid token, set user context
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("auth_source", "supabase")

		log.Printf("✅ Supabase JWT Middleware (Optional): Token validated for user %s", user.Email)

		c.Next()
	}
} 