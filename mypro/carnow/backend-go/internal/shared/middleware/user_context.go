package middleware

import (
	"github.com/gin-gonic/gin"
)

// GetUserIDFromContext extracts user ID from gin context
// This is a simple helper function for getting user ID from middleware context
func GetUserIDFromContext(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}

// GetUserEmailFromContext extracts user email from gin context
func GetUserEmailFromContext(c *gin.Context) string {
	if userEmail, exists := c.Get("user_email"); exists {
		if email, ok := userEmail.(string); ok {
			return email
		}
	}
	return ""
}

// GetUserRoleFromContext extracts user role from gin context
func GetUserRoleFromContext(c *gin.Context) string {
	if userRole, exists := c.Get("user_role"); exists {
		if role, ok := userRole.(string); ok {
			return role
		}
	}
	return ""
}

// IsAuthenticated checks if user is authenticated
func IsAuthenticated(c *gin.Context) bool {
	userID := GetUserIDFromContext(c)
	return userID != ""
} 