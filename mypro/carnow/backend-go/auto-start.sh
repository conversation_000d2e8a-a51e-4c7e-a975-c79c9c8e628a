#!/bin/bash

# CarNow Auto-Start Script
# Forever Plan - Automatic System Startup

echo "🚀 CarNow Auto-Start System Starting..."
echo "📅 $(date)"
echo "=================================="

# Check if system is already running
if pgrep -f "go run cmd/main.go" > /dev/null; then
    echo "⚠️  System is already running. Stopping previous instance..."
    pkill -f "go run cmd/main.go"
    sleep 2
fi

# Check if port 8080 is in use
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  Port 8080 is in use. Stopping process..."
    lsof -ti:8080 | xargs kill -9
    sleep 2
fi

# Start the system automatically
echo "🔄 Starting CarNow Backend System..."
echo "🔐 Using Enhanced JWT Middleware with Supabase"
echo "📊 Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)"

# Start in background with logging
nohup go run cmd/main.go > carnow.log 2>&1 &
PID=$!

echo "✅ System started with PID: $PID"
echo "📝 Logs saved to: carnow.log"
echo "🌐 Health check: http://localhost:8080/health"

# Wait for system to start
echo "⏳ Waiting for system to initialize..."
sleep 5

# Check if system is running
if pgrep -f "go run cmd/main.go" > /dev/null; then
    echo "✅ System is running successfully!"
    
    # Test health endpoint
    if curl -s http://localhost:8080/health > /dev/null; then
        echo "✅ Health check passed!"
        echo "🎉 CarNow System is fully operational!"
    else
        echo "⚠️  Health check failed, but system is running"
    fi
else
    echo "❌ System failed to start!"
    echo "📋 Check logs: tail -f carnow.log"
    exit 1
fi

echo "=================================="
echo "🚀 CarNow Auto-Start Complete!"
echo "📅 $(date)"
echo ""
echo "📋 Useful commands:"
echo "   View logs: tail -f carnow.log"
echo "   Stop system: pkill -f 'go run cmd/main.go'"
echo "   Health check: curl http://localhost:8080/health"
echo "   Restart: ./auto-start.sh" 