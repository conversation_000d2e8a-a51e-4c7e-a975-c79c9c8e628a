#!/bin/bash

# CarNow Auto-Monitor Script
# Forever Plan - Automatic System Monitoring

echo "🔍 CarNow Auto-Monitor Starting..."
echo "📅 $(date)"
echo "=================================="

# Function to check system health
check_health() {
    if curl -s http://localhost:8080/health > /dev/null; then
        echo "✅ System is healthy"
        return 0
    else
        echo "❌ System health check failed"
        return 1
    fi
}

# Function to restart system
restart_system() {
    echo "🔄 Restarting system..."
    pkill -f "go run cmd/main.go"
    sleep 3
    nohup go run cmd/main.go > carnow.log 2>&1 &
    sleep 5
}

# Main monitoring loop
while true; do
    echo ""
    echo "🔍 Checking system status... ($(date))"
    
    # Check if process is running
    if ! pgrep -f "go run cmd/main.go" > /dev/null; then
        echo "❌ System process not found! Restarting..."
        restart_system
        continue
    fi
    
    # Check health endpoint
    if ! check_health; then
        echo "❌ Health check failed! Restarting system..."
        restart_system
        continue
    fi
    
    echo "✅ System is running normally"
    echo "⏳ Next check in 30 seconds..."
    sleep 30
done 