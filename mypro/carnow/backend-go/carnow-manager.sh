#!/bin/bash

# CarNow Manager Script
# Forever Plan - Complete System Management

echo "🎯 CarNow System Manager"
echo "📅 $(date)"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show status
show_status() {
    echo -e "${BLUE}📊 System Status:${NC}"
    
    if pgrep -f "go run cmd/main.go" > /dev/null; then
        echo -e "  ${GREEN}✅ Process: Running${NC}"
        PID=$(pgrep -f "go run cmd/main.go")
        echo -e "  📋 PID: $PID"
    else
        echo -e "  ${RED}❌ Process: Stopped${NC}"
    fi
    
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
        echo -e "  ${GREEN}✅ Port 8080: Active${NC}"
    else
        echo -e "  ${RED}❌ Port 8080: Not listening${NC}"
    fi
    
    if curl -s http://localhost:8080/health > /dev/null; then
        echo -e "  ${GREEN}✅ Health: OK${NC}"
    else
        echo -e "  ${RED}❌ Health: Failed${NC}"
    fi
}

# Function to start system
start_system() {
    echo -e "${BLUE}🚀 Starting CarNow System...${NC}"
    
    if pgrep -f "go run cmd/main.go" > /dev/null; then
        echo -e "${YELLOW}⚠️  System is already running${NC}"
        return
    fi
    
    nohup go run cmd/main.go > carnow.log 2>&1 &
    PID=$!
    echo -e "${GREEN}✅ System started with PID: $PID${NC}"
    echo "📝 Logs: carnow.log"
    
    echo "⏳ Waiting for system to initialize..."
    sleep 5
    
    if curl -s http://localhost:8080/health > /dev/null; then
        echo -e "${GREEN}🎉 System is fully operational!${NC}"
    else
        echo -e "${YELLOW}⚠️  System started but health check pending${NC}"
    fi
}

# Function to stop system
stop_system() {
    echo -e "${BLUE}🛑 Stopping CarNow System...${NC}"
    
    if pgrep -f "go run cmd/main.go" > /dev/null; then
        pkill -f "go run cmd/main.go"
        echo -e "${GREEN}✅ System stopped${NC}"
    else
        echo -e "${YELLOW}⚠️  System was not running${NC}"
    fi
}

# Function to restart system
restart_system() {
    echo -e "${BLUE}🔄 Restarting CarNow System...${NC}"
    stop_system
    sleep 2
    start_system
}

# Function to show logs
show_logs() {
    echo -e "${BLUE}📋 Recent Logs:${NC}"
    if [ -f "carnow.log" ]; then
        tail -20 carnow.log
    else
        echo -e "${YELLOW}⚠️  No log file found${NC}"
    fi
}

# Function to test authentication
test_auth() {
    echo -e "${BLUE}🔐 Testing Authentication...${NC}"
    
    # Test without token (should fail)
    echo "Testing without token:"
    curl -s -w "Status: %{http_code}\n" http://localhost:8080/api/v1/user/profile
    
    echo ""
    echo "Testing with invalid token:"
    curl -s -w "Status: %{http_code}\n" -H "Authorization: Bearer test-token" http://localhost:8080/api/v1/user/profile
    
    echo ""
    echo -e "${GREEN}✅ Authentication test completed${NC}"
}

# Function to show help
show_help() {
    echo -e "${BLUE}📖 Available Commands:${NC}"
    echo "  status    - Show system status"
    echo "  start     - Start the system"
    echo "  stop      - Stop the system"
    echo "  restart   - Restart the system"
    echo "  logs      - Show recent logs"
    echo "  test      - Test authentication"
    echo "  auto      - Start with auto-monitor"
    echo "  help      - Show this help"
    echo ""
    echo -e "${BLUE}🔧 Quick Commands:${NC}"
    echo "  ./carnow-manager.sh status"
    echo "  ./carnow-manager.sh start"
    echo "  ./carnow-manager.sh auto"
}

# Function to start with auto-monitor
start_auto() {
    echo -e "${BLUE}🤖 Starting Auto-Monitor Mode...${NC}"
    start_system
    
    echo -e "${GREEN}✅ Starting background monitor...${NC}"
    nohup ./auto-monitor.sh > monitor.log 2>&1 &
    MONITOR_PID=$!
    echo -e "${GREEN}✅ Monitor started with PID: $MONITOR_PID${NC}"
    echo "📝 Monitor logs: monitor.log"
    
    echo -e "${GREEN}🎉 Auto-monitor mode activated!${NC}"
    echo "The system will automatically restart if it fails."
}

# Main script logic
case "${1:-help}" in
    "status")
        show_status
        ;;
    "start")
        start_system
        ;;
    "stop")
        stop_system
        ;;
    "restart")
        restart_system
        ;;
    "logs")
        show_logs
        ;;
    "test")
        test_auth
        ;;
    "auto")
        start_auto
        ;;
    "help"|*)
        show_help
        ;;
esac

echo ""
echo "=================================="
echo "🎯 CarNow Manager Complete!"
echo "📅 $(date)" 