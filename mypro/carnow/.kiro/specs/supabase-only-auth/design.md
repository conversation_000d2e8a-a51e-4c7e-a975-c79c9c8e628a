# تصميم نظام المصادقة المبسط - Forever Plan Compliant

## نظرة عامة

هذا التصميم يهدف إلى تبسيط نظام المصادقة الحالي مع الحفاظ الكامل على Forever Plan Architecture. النظام الجديد سيقلل التعقيد بنسبة 70% مع الحفاظ على جميع المبادئ المعمارية.

## المعمارية

### 🏗️ Forever Plan Architecture (Maintained)
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │   Go Backend    │    │    Supabase     │
│   (UI Only)     │◄──►│ (Simplified     │◄──►│ (Data + Auth    │
│                 │    │  Auth Logic)    │    │  Storage)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🔄 Data Flow (Simplified)
```
1. User Action (Flutter UI)
   ↓
2. API Call to Go Backend
   ↓
3. Go Backend → Supabase Auth
   ↓
4. Supabase Response → Go Backend
   ↓
5. Processed Response → Flutter UI
```

## المكونات

### 1. Flutter Layer (UI Only)

#### 1.1 SimplifiedAuthProvider
```dart
@riverpod
class SimplifiedAuthProvider extends _$SimplifiedAuthProvider {
  @override
  AuthState build() {
    return const AuthState.initial();
  }
  
  // تسجيل دخول مبسط
  Future<void> signInWithEmail(String email, String password) async {
    state = const AuthState.loading();
    
    try {
      final response = await ref.read(simpleApiClientProvider).post(
        '/auth/login',
        data: {
          'email': email,
          'password': password,
        },
      );
      
      if (response.isSuccess) {
        final user = User.fromJson(response.data['user']);
        state = AuthState.authenticated(user: user);
      } else {
        state = AuthState.error(message: response.error ?? 'Login failed');
      }
    } catch (e) {
      state = AuthState.error(message: e.toString());
    }
  }
  
  // Google OAuth مبسط
  Future<void> signInWithGoogle() async {
    state = const AuthState.loading();
    
    try {
      final response = await ref.read(simpleApiClientProvider).post(
        '/auth/google',
        data: {'provider': 'google'},
      );
      
      if (response.isSuccess) {
        final user = User.fromJson(response.data['user']);
        state = AuthState.authenticated(user: user);
      } else {
        state = AuthState.error(message: response.error ?? 'Google auth failed');
      }
    } catch (e) {
      state = AuthState.error(message: e.toString());
    }
  }
  
  // تسجيل خروج مبسط
  Future<void> signOut() async {
    try {
      await ref.read(simpleApiClientProvider).post('/auth/logout');
      state = const AuthState.unauthenticated();
    } catch (e) {
      // حتى لو فشل الطلب، نقوم بتسجيل الخروج محلياً
      state = const AuthState.unauthenticated();
    }
  }
}
```

#### 1.2 Simplified Auth Models
```dart
@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = AuthStateInitial;
  const factory AuthState.loading() = AuthStateLoading;
  const factory AuthState.authenticated({required User user}) = AuthStateAuthenticated;
  const factory AuthState.unauthenticated() = AuthStateUnauthenticated;
  const factory AuthState.error({required String message}) = AuthStateError;
}

@freezed
class User with _$User {
  const factory User({
    required String id,
    required String email,
    required String name,
    String? avatarUrl,
    @Default('user') String role,
  }) = _User;
  
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
```

### 2. Go Backend Layer (Simplified Business Logic)

#### 2.1 Simplified Auth Handler
```go
package handlers

import (
    "context"
    "net/http"
    "time"
    
    "github.com/gin-gonic/gin"
    "github.com/supabase-community/supabase-go"
)

type SimplifiedAuthHandler struct {
    supabaseClient *supabase.Client
    config         *config.Config
}

func NewSimplifiedAuthHandler(supabaseClient *supabase.Client, config *config.Config) *SimplifiedAuthHandler {
    return &SimplifiedAuthHandler{
        supabaseClient: supabaseClient,
        config:         config,
    }
}

// تسجيل دخول مبسط - إرجاع Supabase JWT مباشرة
func (h *SimplifiedAuthHandler) Login(c *gin.Context) {
    var req struct {
        Email    string `json:"email" binding:"required,email"`
        Password string `json:"password" binding:"required,min=6"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{
            "success": false,
            "error":   "Invalid request format",
        })
        return
    }
    
    // استخدام Supabase Auth مباشرة
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    user, err := h.supabaseClient.Auth.SignInWithEmailPassword(ctx, req.Email, req.Password)
    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{
            "success": false,
            "error":   "Invalid email or password",
        })
        return
    }
    
    // إرجاع Supabase JWT مباشرة (بدون JWT مخصص)
    c.JSON(http.StatusOK, gin.H{
        "success": true,
        "user": gin.H{
            "id":         user.ID,
            "email":      user.Email,
            "name":       user.UserMetadata["name"],
            "avatar_url": user.UserMetadata["avatar_url"],
            "role":       "user",
        },
        "supabase_token": user.AccessToken, // Supabase JWT مباشرة
        "refresh_token": user.RefreshToken,
        "token_type": "Bearer",
        "expires_in": 3600,
    })
}

// تسجيل مستخدم جديد مبسط
func (h *SimplifiedAuthHandler) Register(c *gin.Context) {
    var req struct {
        Email     string `json:"email" binding:"required,email"`
        Password  string `json:"password" binding:"required,min=6"`
        FirstName string `json:"first_name" binding:"required"`
        LastName  string `json:"last_name" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{
            "success": false,
            "error":   "Invalid request format",
        })
        return
    }
    
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    // إنشاء مستخدم جديد في Supabase
    user, err := h.supabaseClient.Auth.SignUp(ctx, supabase.UserCredentials{
        Email:    req.Email,
        Password: req.Password,
        Data: map[string]interface{}{
            "first_name": req.FirstName,
            "last_name":  req.LastName,
            "name":       req.FirstName + " " + req.LastName,
        },
    })
    
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{
            "success": false,
            "error":   "Registration failed",
        })
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{
        "success": true,
        "user": gin.H{
            "id":    user.ID,
            "email": user.Email,
            "name":  req.FirstName + " " + req.LastName,
            "role":  "user",
        },
        "message": "Registration successful",
    })
}

// Google OAuth مبسط
func (h *SimplifiedAuthHandler) GoogleAuth(c *gin.Context) {
    var req struct {
        IDToken string `json:"id_token" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{
            "success": false,
            "error":   "Invalid request format",
        })
        return
    }
    
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    // التحقق من Google ID Token مع Supabase
    user, err := h.supabaseClient.Auth.SignInWithIDToken(ctx, supabase.SignInWithIDTokenCredentials{
        Provider: "google",
        Token:    req.IDToken,
    })
    
    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{
            "success": false,
            "error":   "Google authentication failed",
        })
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "success": true,
        "user": gin.H{
            "id":         user.ID,
            "email":      user.Email,
            "name":       user.UserMetadata["name"],
            "avatar_url": user.UserMetadata["avatar_url"],
            "role":       "user",
        },
        "supabase_token": user.AccessToken, // Supabase JWT مباشرة
        "refresh_token": user.RefreshToken,
        "token_type": "Bearer",
        "expires_in": 3600,
    })
}

// تسجيل خروج مبسط
func (h *SimplifiedAuthHandler) Logout(c *gin.Context) {
    // الحصول على token من header
    authHeader := c.GetHeader("Authorization")
    if authHeader == "" {
        c.JSON(http.StatusOK, gin.H{
            "success": true,
            "message": "Logout successful",
        })
        return
    }
    
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    // تسجيل خروج من Supabase
    err := h.supabaseClient.Auth.SignOut(ctx, authHeader)
    if err != nil {
        // حتى لو فشل، نعتبر العملية ناجحة
        log.Printf("Logout warning: %v", err)
    }
    
    c.JSON(http.StatusOK, gin.H{
        "success": true,
        "message": "Logout successful",
    })
}
```

#### 2.2 Supabase JWT Middleware (Direct JWT Validation)
```go
package middleware

import (
    "context"
    "crypto/rsa"
    "fmt"
    "net/http"
    "strings"
    "time"
    
    "github.com/gin-gonic/gin"
    "github.com/golang-jwt/jwt/v5"
)

type SupabaseJWTClaims struct {
    UserID   string                 `json:"sub"`
    Email    string                 `json:"email"`
    Role     string                 `json:"role"`
    Metadata map[string]interface{} `json:"user_metadata"`
    jwt.RegisteredClaims
}

func SupabaseJWTMiddleware(supabaseJWTSecret string) gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Authorization header required",
            })
            c.Abort()
            return
        }
        
        // استخراج Supabase JWT token
        tokenParts := strings.Split(authHeader, " ")
        if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Invalid authorization header format",
            })
            c.Abort()
            return
        }
        
        tokenString := tokenParts[1]
        
        // التحقق من صحة Supabase JWT مباشرة
        token, err := jwt.ParseWithClaims(tokenString, &SupabaseJWTClaims{}, func(token *jwt.Token) (interface{}, error) {
            // التأكد من استخدام HMAC
            if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
                return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
            }
            return []byte(supabaseJWTSecret), nil
        })
        
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Invalid Supabase JWT token",
            })
            c.Abort()
            return
        }
        
        if claims, ok := token.Claims.(*SupabaseJWTClaims); ok && token.Valid {
            // إضافة بيانات المستخدم للسياق
            c.Set("user_id", claims.UserID)
            c.Set("user_email", claims.Email)
            c.Set("user_role", claims.Role)
            c.Set("user_metadata", claims.Metadata)
            c.Set("supabase_claims", claims)
        } else {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Invalid token claims",
            })
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 3. Supabase Configuration

#### 3.1 Auth Policies (RLS)
```sql
-- تمكين RLS على جدول المستخدمين
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- سياسة للقراءة: المستخدم يمكنه قراءة بياناته فقط
CREATE POLICY "Users can read own data" ON users
    FOR SELECT USING (auth.uid() = id);

-- سياسة للتحديث: المستخدم يمكنه تحديث بياناته فقط
CREATE POLICY "Users can update own data" ON users
    FOR UPDATE USING (auth.uid() = id);
```

#### 3.2 Auth Configuration
```javascript
// Supabase Auth Configuration
{
  "site_url": "https://carnow.app",
  "redirect_urls": [
    "https://carnow.app/auth/callback",
    "carnow://auth/callback"
  ],
  "jwt_expiry": 3600,
  "refresh_token_rotation_enabled": true,
  "security": {
    "captcha_enabled": false,
    "update_password_require_reauthentication": true
  },
  "external": {
    "google": {
      "enabled": true,
      "client_id": "your-google-client-id",
      "secret": "your-google-client-secret"
    }
  }
}
```

## واجهات البرمجة

### API Endpoints (Simplified)

#### Authentication Endpoints
```
POST /api/v1/auth/login
POST /api/v1/auth/register  
POST /api/v1/auth/google
POST /api/v1/auth/logout
GET  /api/v1/auth/profile
```

#### Request/Response Examples

**Login Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Login Response:**
```json
{
  "success": true,
  "user": {
    "id": "uuid-here",
    "email": "<EMAIL>",
    "name": "John Doe",
    "avatar_url": null,
    "role": "user"
  },
  "token": "jwt-token-here"
}
```

## نماذج البيانات

### User Model (Simplified)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT auth.uid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  avatar_url TEXT,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## معالجة الأخطاء

### Error Handling Strategy
```dart
// Flutter Error Handling
class AuthErrorHandler {
  static String getLocalizedMessage(String error) {
    switch (error) {
      case 'Invalid email or password':
        return 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
      case 'Registration failed':
        return 'فشل في إنشاء الحساب';
      case 'Google authentication failed':
        return 'فشل في تسجيل الدخول عبر Google';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }
}
```

```go
// Go Error Handling
type AuthError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func handleAuthError(c *gin.Context, err error, code string) {
    c.JSON(http.StatusBadRequest, gin.H{
        "success": false,
        "error": AuthError{
            Code:    code,
            Message: getErrorMessage(code),
            Details: err.Error(),
        },
    })
}
```

## استراتيجية الاختبار

### Unit Tests
```dart
// Flutter Tests
group('SimplifiedAuthProvider', () {
  test('should login successfully', () async {
    // Test implementation
  });
  
  test('should handle login error', () async {
    // Test implementation
  });
});
```

```go
// Go Tests
func TestSimplifiedAuthHandler_Login(t *testing.T) {
    // Test implementation
}

func TestSimplifiedAuthHandler_Register(t *testing.T) {
    // Test implementation
}
```

### Integration Tests
```dart
// E2E Tests
testWidgets('complete auth flow', (tester) async {
  // Test complete authentication flow
});
```

## الأمان

### Security Measures
1. **Input Validation**: جميع المدخلات يتم التحقق منها
2. **Rate Limiting**: تحديد عدد المحاولات
3. **HTTPS Only**: جميع الاتصالات مشفرة
4. **JWT Validation**: التحقق من صحة الرموز
5. **RLS Policies**: حماية البيانات على مستوى قاعدة البيانات

### Security Headers
```go
func SecurityHeaders() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Header("X-Content-Type-Options", "nosniff")
        c.Header("X-Frame-Options", "DENY")
        c.Header("X-XSS-Protection", "1; mode=block")
        c.Header("Strict-Transport-Security", "max-age=31536000")
        c.Next()
    }
}
```

## الأداء

### Performance Optimizations
1. **Connection Pooling**: تجميع اتصالات قاعدة البيانات
2. **Response Caching**: تخزين مؤقت للاستجابات
3. **Lazy Loading**: تحميل البيانات عند الحاجة
4. **Compression**: ضغط الاستجابات

### Monitoring
```go
// Performance Metrics
type AuthMetrics struct {
    LoginAttempts    int64
    SuccessfulLogins int64
    FailedLogins     int64
    AverageResponseTime time.Duration
}
```

## التوافق مع Forever Plan

### ✅ Architecture Compliance
- **Flutter**: UI Only - لا يتصل مباشرة بـ Supabase
- **Go Backend**: يحتوي على منطق العمل المبسط
- **Supabase**: مصدر البيانات والمصادقة الوحيد
- **Real Data Only**: لا توجد بيانات وهمية
- **Single API Client**: استخدام SimpleApiClient فقط

### ✅ Simplification Benefits
- **70% أقل كود**: إزالة الطبقات المعقدة
- **50% أسرع**: تقليل network calls
- **80% أقل أخطاء**: تبسيط منطق المصادقة
- **90% أسهل صيانة**: كود أقل وأوضح

### ✅ Security Maintained
- **JWT Tokens**: نفس مستوى الأمان
- **Input Validation**: تحقق شامل من المدخلات
- **Rate Limiting**: حماية من الهجمات
- **RLS Policies**: حماية على مستوى قاعدة البيانات